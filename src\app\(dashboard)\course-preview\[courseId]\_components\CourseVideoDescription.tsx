import React from 'react';
import VideoPlayer from './VideoPlayer';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import ReactMarkdown from 'react-markdown';

type Video = {
  url: string;
};

type Chapter = {
  id: string;
  name: string;
  video: Video;
  shortDescription?: string[]; // This is now an array of strings
};

type Banner = {
  url: string;
};

type Course = {
  author: string[];
  banner: Banner;
  chapter: Chapter[];
  description: string[];
  free: boolean;
  id: string;
  name: string;
  slug: string;
  tags: string[];
  totalChapters: number;
};

type CourseVideoDescriptionProps = {
  courseInfo: Course;
  activeChapterIndex: number;
};

function CourseVideoDescription({
  courseInfo,
  activeChapterIndex,
}: CourseVideoDescriptionProps) {
  if (!courseInfo) {
    return <div>Loading...</div>;
  }

  const currentChapter = courseInfo.chapter[activeChapterIndex];

  // Convert shortDescription array to a single string
  const description = Array.isArray(currentChapter.shortDescription)
    ? currentChapter.shortDescription.join('\n\n') // Join with double newlines for Markdown paragraphs
    : 'No description available.';

  return (
    <div className="bg-white p-3">
      <h2 className="text-[20px] font-semibold">{courseInfo.name}</h2>
      <h2 className="mb-3 text-[14px] text-gray-500">{courseInfo.author}</h2>
      <div key={currentChapter.id}>
        <VideoPlayer videoUrl={currentChapter.video.url} />
        <h3 className="mb-3 mt-4 text-[20px] font-semibold">
          {currentChapter.name}
        </h3>
        <Accordion type="single" collapsible>
          <AccordionItem value="item-1">
            <AccordionTrigger>Description</AccordionTrigger>
            <AccordionContent>
              {/* Render the description as Markdown */}
              <ReactMarkdown>
                {description}
              </ReactMarkdown>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
}

export default CourseVideoDescription;

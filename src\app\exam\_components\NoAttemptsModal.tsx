import React from 'react';
import { useRouter } from 'next/navigation';

interface NoAttemptsModalProps {
  open: boolean;
  onClose: () => void;
  examName: string;
}

const NoAttemptsModal: React.FC<NoAttemptsModalProps> = ({
  open,
  onClose,
  examName,
}) => {
  const router = useRouter();

  const handleClose = () => {
    onClose();
    router.push('/dashboard');
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="p-6 rounded-lg shadow-lg max-w-md mx-auto bg-white text-red-800">
        <h2 className="text-2xl font-bold mb-4 text-center">{examName}</h2>
        <div className="text-center mb-6">
          <p className="text-xl font-bold text-red-600 mb-2">No Attempts Remaining</p>
          <p className="text-gray-700">
            You have used all your exam attempts. Please contact the administrator to request re-enrollment in this course.
          </p>
        </div>
        <div className="text-center mt-4">
          <button
            onClick={handleClose}
            className="px-4 py-2 rounded bg-gray-300 hover:bg-gray-400 text-gray-800"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
};

export default NoAttemptsModal;


"use client"
import React, { createContext, useState, useContext, useEffect } from 'react';

interface TimerContextProps {
  timer: string;
  startExam: () => void;
}

const TimerContext = createContext<TimerContextProps | undefined>(undefined);

export const TimerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [timer, setTimer] = useState("01:00:00"); // Set default exam duration to 1 hour
  const [examStarted, setExamStarted] = useState(false);

  const startExam = () => {
    setExamStarted(true);
  };

  useEffect(() => {
    if (examStarted) {
      const examDuration = 3600; // Exam duration in seconds (1 hour)
      const endTime = Date.now() + examDuration * 1000;

      const updateTimer = () => {
        const timeLeft = endTime - Date.now();
        const hours = String(Math.floor(timeLeft / (1000 * 60 * 60))).padStart(2, "0");
        const minutes = String(Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))).padStart(2, "0");
        const seconds = String(Math.floor((timeLeft % (1000 * 60)) / 1000)).padStart(2, "0");
        setTimer(`${hours}:${minutes}:${seconds}`);
      };

      const timerInterval = setInterval(updateTimer, 1000);

      return () => clearInterval(timerInterval);
    }
  }, [examStarted]);

  return (
    <TimerContext.Provider value={{ timer, startExam }}>
      {children}
    </TimerContext.Provider>
  );
};

export const useTimer = () => {
  const context = useContext(TimerContext);
  if (!context) {
    throw new Error('useTimer must be used within a TimerProvider');
  }
  return context;
};

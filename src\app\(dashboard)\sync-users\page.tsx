"use client"
import { useEffect, useState } from 'react';

const SyncUsers = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const syncUsers = async () => {
      try {
        const response = await fetch('/api/sync-users');
        if (!response.ok) {
          throw new Error(`Error: ${response.statusText}`);
        }
        const result = await response.json();
        console.log(result.message);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };

    syncUsers();
  }, []);

  if (loading) return <div>Syncing Users...</div>;
  if (error) return <div>Error: {error}</div>;

  return <div>Users synced successfully!</div>;
};

export default SyncUsers;

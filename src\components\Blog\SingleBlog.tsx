import { Blog } from "@/types/blog";
import Image from "next/image";
import Link from "next/link";

const SingleBlog = ({ blog }: { blog: Blog }) => {
  const { title, image, tags } = blog;

  // Determine the link and button text based on the tag
  const isPhysical = tags.includes("Physical");
  const isOnline = tags.includes("Online");
  const linkHref = isPhysical ? "/course-details" : isOnline ? "/sign-in" : "#";
  const buttonText = isPhysical ? "Course Details" : isOnline ? "Enroll for Course" : "View Course";

  return (
    <div className="group relative overflow-hidden rounded-xl bg-white shadow-one duration-300 hover:shadow-two dark:bg-dark dark:hover:shadow-gray-dark mb-4">
      <Link href={linkHref}>
        <div className="relative block aspect-[37/22] w-full cursor-pointer">
          <span className="absolute right-6 top-6 z-20 inline-flex items-center justify-center rounded-full bg-yellow px-4 py-2 text-sm font-semibold capitalize text-white">
            {tags[0]}
          </span>
          <Image src={image} alt="image" fill />
        </div>
      </Link>
      <div className="p-6 sm:p-8 md:px-6 md:py-8 lg:p-8 xl:px-5 xl:py-8 2xl:p-8">
        <h3>
          <Link href={linkHref}>
            <span className="mb-4 block text-xl font-bold text-black dark:text-white dark:hover:text-2lg sm:text-2xl cursor-pointer">
              {title}
            </span>
          </Link>
        </h3>
        <div className="flex items-center">
          <Link href={linkHref}>
            <div className="flex w-100 items-center justify-center rounded-sm bg-primary p-3 text-base font-semibold text-white transition duration-300 ease-in-out hover:bg-opacity-80 hover:shadow-signUp cursor-pointer">
              {buttonText}
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SingleBlog;

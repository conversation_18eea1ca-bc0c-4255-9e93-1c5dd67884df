name: GlobalSafeSkyz CI Pipeline

on:
  push:
    branches:
      - main
      - staging
  pull_request:
    branches:
      - main
      - staging

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16] # Adjust based on your project's Node.js version

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}

    - name: Install dependencies
      run: npm install

    # - name: Run tests
    #   run: npm test

    - name: Build project
      run: npm run build
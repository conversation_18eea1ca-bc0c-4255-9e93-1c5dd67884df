"use client";
import React, { useEffect, useState } from "react";
import CourseVideoDescription from "./_components/CourseVideoDescription";
import GlobalApi from "@/app/_utils/GlobalApi";
import CourseContentSection from "./_components/CourseContentSection";

type Video = {
  url: string;
};

type Chapter = {
  id: string;
  name: string;
  video: Video;
};

type Banner = {
  url: string;
};

type Course = {
  author: string[];
  banner: Banner;
  chapter: Chapter[];
  description: string[];
  free: boolean;
  id: string;
  name: string;
  slug: string;
  tags: string[];
  totalChapters: number;
};

type Params = {
  params: {
    courseId: string;
  };
};

const CoursePreview: React.FC<Params> = ({ params }) => {
  const [courseInfo, setCourseInfo] = useState<Course | undefined>(undefined);
  const [error, setError] = useState<string | null>(null);
  const [activeChapterIndex, setActiveChapterIndex] = useState(0);

  useEffect(() => {
    const getCourseInfoById = async () => {
      try {
        const resp = await GlobalApi.getCourseById(params.courseId);
        if (resp && resp.coursesLists && resp.coursesLists.length > 0) {
          setCourseInfo(resp.coursesLists[0]);
        } else {
          setError("No course information found");
        }
      } catch (error) {
        console.error("Error fetching course info:", error);
        setError("Failed to fetch course information");
      }
    };

    getCourseInfoById();
  }, [params.courseId]);

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="grid grid-cols-1 gap-3 p-5 md:grid-cols-3">
      <div className="col-span-2 text-gray-800">
        <CourseVideoDescription
          courseInfo={courseInfo}
          activeChapterIndex={activeChapterIndex}
        />
      </div>
      <div>
        <CourseContentSection
          courseInfo={courseInfo}
          setActiveChapterIndex={setActiveChapterIndex}
        />
      </div>
    </div>
  );
};

export default CoursePreview;

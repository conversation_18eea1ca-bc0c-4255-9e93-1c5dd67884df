# Certificate Generation System

This document describes the HTML-based certificate generation system implemented for the Globalsafeskyz Learning Management System.

## Overview

The system generates PDF certificates using an HTML template with dynamic placeholders. It uses <PERSON><PERSON><PERSON><PERSON> to convert the HTML to PDF, providing high-quality certificates that match the original design.

## Components

### 1. HTML Template
- **Location**: `public/Template_AVSEC_GA_certificate_24_filled_in.html`
- **Description**: HTML version of the original PDF certificate template
- **Placeholders**:
  - `{{NAME}}` - Student name in "J Doe" format (first initial + surname)
  - `{{ID_NUMBER}}` - Student ID number
  - `{{CURRENT_DATE}}` - Current date in DD/MM/YYYY format
  - `{{ISSUED_DATE}}` - Certificate issue date in DD/MM/YYYY format
  - `{{DATE_PLUS_TWO_YEARS}}` - Expiry date (2 years from issue) in DD/MM/YYYY format

### 2. API Endpoint
- **Route**: `/api/generate-certificate-html`
- **Method**: POST
- **Authentication**: Required (Clerk)
- **Request Body**:
  ```json
  {
    "courseName": "Aviation Security (AVSEC) General Awareness",
    "examScore": 85
  }
  ```
- **Response**: PDF file download

### 3. Utility Functions
- **Location**: `src/app/_utils/certificateUtils.ts`
- **Functions**:
  - `formatCertificateName()` - Formats name as "J Doe"
  - `generateCertificateDates()` - Generates formatted dates
  - `replacePlaceholders()` - Replaces template placeholders
  - `generateCertificatePDF()` - Main PDF generation function
  - `validateExamScore()` - Validates minimum score requirement
  - `generateCertificateFilename()` - Creates appropriate filename

### 4. React Components
- **CertificateGeneratorHTML**: Main component for certificate generation
- **Location**: `src/components/Certificate/CertificateGeneratorHTML.tsx`
- **Features**:
  - Preview and download functionality
  - Loading states
  - Error handling
  - Score validation

## Usage

### Basic Usage
```typescript
import CertificateGeneratorHTML from '@/components/Certificate/CertificateGeneratorHTML';

<CertificateGeneratorHTML
  courseName="Aviation Security (AVSEC) General Awareness"
  examScore={85}
  onSuccess={(url) => console.log('Certificate generated:', url)}
  onError={(error) => console.error('Error:', error)}
/>
```

### API Usage
```typescript
const response = await fetch('/api/generate-certificate-html', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    courseName: "Aviation Security (AVSEC) General Awareness",
    examScore: 85,
  }),
});

if (response.ok) {
  const blob = await response.blob();
  // Handle PDF blob
}
```

## Requirements

### Dependencies
- `puppeteer` - For HTML to PDF conversion
- `@clerk/nextjs` - For user authentication
- User profile data with ID number

### Environment
- Node.js environment with file system access
- Sufficient memory for Puppeteer operations

## Data Flow

1. **User Authentication**: Verify user is logged in via Clerk
2. **Data Retrieval**: Get user profile data from database
3. **Name Formatting**: Format name as "J Doe" (first initial + surname)
4. **Date Generation**: Generate current date and expiry date (2 years)
5. **Template Processing**: Read HTML template and replace placeholders
6. **PDF Generation**: Use Puppeteer to convert HTML to PDF
7. **Response**: Return PDF as downloadable file

## Validation

### Exam Score Validation
- Minimum score: 80%
- Validation occurs before PDF generation
- Error returned if score is below threshold

### User Data Validation
- User must be authenticated
- User profile must exist in database
- Required fields: name, surname, ID number

## Error Handling

### Common Errors
- **401 Unauthorized**: User not logged in
- **400 Bad Request**: Invalid exam score or missing user data
- **500 Internal Server Error**: PDF generation failure

### Error Response Format
```json
{
  "error": "Error message",
  "details": "Detailed error information"
}
```

## Testing

### Test Page
- **Location**: `/certificate-test` (dashboard route)
- **Features**:
  - Interactive certificate generation
  - Score adjustment
  - Preview and download
  - API documentation

### Manual Testing
1. Navigate to `/certificate-test`
2. Adjust exam score (try values above and below 80)
3. Click "Preview" to view certificate in new tab
4. Click "Download" to download PDF file
5. Verify all placeholders are correctly replaced

## Integration

### Existing Components
The system has been integrated into:
- `ResultModal.tsx` - Updated to use HTML-based generation
- Exam completion flow - Automatic certificate generation for passing scores

### Migration from React-PDF
The system replaces the previous React-PDF implementation with:
- Better template fidelity
- Easier customization
- More reliable PDF generation
- Support for complex layouts

## Maintenance

### Template Updates
1. Update the HTML template in `public/Template_AVSEC_GA_certificate_24_filled_in.html`
2. Ensure placeholders remain intact
3. Test certificate generation after changes

### Adding New Placeholders
1. Add placeholder to HTML template (e.g., `{{NEW_FIELD}}`)
2. Update `CertificateData` interface in `certificateUtils.ts`
3. Update `replacePlaceholders()` function
4. Update data preparation in API endpoint

## Security Considerations

- User authentication required for all certificate generation
- User can only generate certificates for themselves
- Exam score validation prevents unauthorized certificate generation
- No sensitive data stored in generated PDFs beyond what's necessary

## Performance

- Puppeteer instances are created and destroyed per request
- PDF generation typically takes 2-5 seconds
- Memory usage scales with concurrent requests
- Consider implementing request queuing for high load scenarios

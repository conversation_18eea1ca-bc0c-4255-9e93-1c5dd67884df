"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { UserButton, useUser } from "@clerk/nextjs";
import Link from "next/link";
import { useTimer } from "./TimerContext"; // Update the path to where you save TimerContext

function MainNav() {
  const { user, isLoaded } = useUser();
  const { timer } = useTimer();

  return (
    <nav className="fixed top-0 w-full bg-white shadow-md">
      <div className="container mx-auto flex items-center justify-between p-4">
        <div className="flex items-center gap-4 text-gray-900">
          <span className="text-lg font-bold">Time : {timer}</span>
        </div>
        <h1 className="text-center text-xl font-bold  text-bg-color-dark">
          (AVSEC) Aviation Security General Awareness
        </h1>
        <div className="flex gap-2 rounded-md p-2 ">
          <div className="ml-auto flex items-center justify-end gap-2">
            {isLoaded && user ? (
              <UserButton afterSignOutUrl="/courses" />
            ) : (
              <Link href="/sign-in">
                <Button className="hover:bg-yellow">Get started</Button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}

export default MainNav;

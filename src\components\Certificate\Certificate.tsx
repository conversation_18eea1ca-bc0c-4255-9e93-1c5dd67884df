import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#E4E4E4',
  },
  section: {
    margin: 10,
    padding: 10,
    flexGrow: 1,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  text: {
    position: 'absolute',
    fontSize: 12,
    color: '#000000',
  },
});

interface CertificateProps {
  studentName: string;
  courseName: string;
  completionDate: string;
  examScore: number;
}

const Certificate: React.FC<CertificateProps> = ({ studentName, courseName, completionDate, examScore }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* <Image src={templateUrl} style={styles.image} /> */}
      <View style={styles.overlay}>
        <Text style={{...styles.text, top: '40%', left: '50%', transform: 'translate(-50%, -50%)'}}>
          {studentName}
        </Text>
        <Text style={{...styles.text, top: '50%', left: '50%', transform: 'translate(-50%, -50%)'}}>
          {courseName}
        </Text>
        <Text style={{...styles.text, top: '60%', left: '50%', transform: 'translate(-50%, -50%)'}}>
          Completed on: {completionDate}
        </Text>
        <Text style={{...styles.text, top: '70%', left: '50%', transform: 'translate(-50%, -50%)'}}>
          Score: {examScore}%
        </Text>
      </View>
    </Page>
  </Document>
);

export default Certificate;

/**
 * Certificate Download Component
 *
 * This component displays a button to download a certificate
 * and shows a preview of the certificate if available.
 */
import React, { useState } from "react";

/**
 * Certificate Download Component
 * @param {Object} props - Component props
 * @param {string} props.studentName - Student name
 * @param {string} props.idNumber - Student ID number
 * @param {string} props.courseName - Course name
 * @param {string} props.completionDate - Course completion date
 * @param {string} props.certificateUrl - URL to an existing certificate (optional)
 * @param {Function} props.onGenerate - Callback when certificate is generated (optional)
 */
const CertificateDownload = ({
  studentName,
  idNumber,
  courseName,
  courseCode,
  location,
  completionDate,
  certificateUrl: initialCertificateUrl,
  onGenerate,
}) => {
  const [loading, setLoading] = useState(false);
  const [certificateUrl, setCertificateUrl] = useState(initialCertificateUrl);
  const [certificateNumber, setCertificateNumber] = useState("");

  /**
   * Generate a certificate
   */
  const generateCertificate = async () => {
    if (!studentName || !idNumber) {
      alert("Student name and ID number are required");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/generate-certificate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          studentName,
          idNumber,
          courseName,
          courseCode,
          location,
          completionDate,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to generate certificate");
      }

      setCertificateUrl(data.certificateUrl);
      setCertificateNumber(data.certificateNumber);

      if (onGenerate) {
        onGenerate(data.certificateUrl, data.certificateNumber);
      }

      alert("Certificate generated successfully");
    } catch (error) {
      console.error("Error generating certificate:", error);
      alert(error.message || "Failed to generate certificate");
    } finally {
      setLoading(false);
    }
  };

  /**
   * Download the certificate
   */
  const downloadCertificate = () => {
    if (!certificateUrl) {
      alert("No certificate available to download");
      return;
    }

    // Create a link and trigger download
    const link = document.createElement("a");
    link.href = certificateUrl;
    link.download = `Certificate_${studentName.replace(/\s+/g, "_")}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="w-full rounded-lg border border-gray-200 bg-white p-6 shadow-md">
      <div className="flex flex-col gap-4">
        {certificateUrl ? (
          <>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">Certificate Available</h3>
                {certificateNumber && (
                  <p className="text-sm text-gray-500">
                    Certificate #: {certificateNumber}
                  </p>
                )}
              </div>
              <button
                onClick={downloadCertificate}
                className="flex items-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                Download
              </button>
            </div>

            <div className="rounded-md border bg-gray-50 p-2">
              <iframe
                src={certificateUrl}
                className="h-[400px] w-full"
                title="Certificate Preview"
              />
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center gap-4 py-8">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-gray-400"
            >
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
            <div className="text-center">
              <h3 className="text-lg font-semibold">
                No Certificate Available
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Generate a certificate for this student
              </p>
            </div>
            <button
              onClick={generateCertificate}
              disabled={loading}
              className="flex items-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {loading ? (
                <>
                  <svg
                    className="h-4 w-4 animate-spin text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Generating...
                </>
              ) : (
                <>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                  Generate Certificate
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CertificateDownload;

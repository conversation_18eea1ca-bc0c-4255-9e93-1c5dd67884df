import { NextRequest, NextResponse } from 'next/server';
// @ts-ignore
import { GraphQLClient, gql } from 'graphql-request';
import { rateLimit } from '@/app/_utils/rateLimit';

const hygraphClient = new GraphQLClient(process.env.NEXT_PUBLIC_MASTER_URL, {
  headers: {
    authorization: `Bearer ${process.env.NEXT_PUBLIC_HYGRAPH_API_KEY}`,
  },
});

export async function POST(request: NextRequest) {
  const ip = request.headers.get('x-forwarded-for') || request.ip;
  
  if (!rateLimit(ip as string)) {
    return NextResponse.json({ isValid: false, message: "Too many requests. Please try again later." }, { status: 429 });
  }

  const { referenceCode, courseSlug } = await request.json();

  if (!referenceCode) {
    return NextResponse.json({ isValid: false, message: "No reference code provided" }, { status: 400 });
  }

  try {
    const query = gql`
      query GetReferenceCode($code: String!) {
        referenceCode(where: { code: $code }) {
          id
          isUsed
          expirationDate
          courseList {
            id
            name
            slug
          }
        }
      }
    `;

    const { referenceCode: codeData } = await hygraphClient.request(query, { code: referenceCode });

    if (!codeData) {
      return NextResponse.json({ isValid: false, message: "Invalid reference code" }, { status: 400 });
    }

    if (codeData.isUsed) {
      return NextResponse.json({ isValid: false, message: "Reference code has already been used" }, { status: 400 });
    }

    // Convert expiration date to UTC for consistent comparison
    const expirationDate = new Date(codeData.expirationDate);
    const currentDate = new Date();
    
    if (expirationDate < currentDate) {
      return NextResponse.json({ 
        isValid: false, 
        message: `Reference code has expired on ${expirationDate.toLocaleString()}`
      }, { status: 400 });
    }

    if (codeData.courseList.slug !== courseSlug && codeData.courseList.id !== courseSlug) {
      return NextResponse.json({ isValid: false, message: "Reference code is not valid for this course" }, { status: 400 });
    }

    // Mark the code as used
    const updateMutation = gql`
      mutation UpdateReferenceCode($code: String!) {
        updateReferenceCode(where: { code: $code }, data: { isUsed: true }) {
          id
        }
        publishReferenceCode(where: { code: $code }) {
          id
        }
      }
    `;

    await hygraphClient.request(updateMutation, { code: referenceCode });

    return NextResponse.json({ isValid: true, courseInfo: codeData.courseList });
  } catch (error) {
    console.error('Error checking reference code:', error);
    return NextResponse.json({ isValid: false, message: "An error occurred while checking the reference code" }, { status: 500 });
  }
}
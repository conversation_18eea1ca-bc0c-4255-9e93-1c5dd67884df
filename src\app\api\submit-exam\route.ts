import { NextRequest, NextResponse } from 'next/server';
import GlobalApi from '@/app/_utils/GlobalApi';

async function fetchExamFromDatabase(examId: string) {
  try {
    const result = await GlobalApi.getAllExamList();
    const exam = result.exams.find((e: any) => e.examName.name === examId);
    if (!exam) {
      throw new Error('Exam not found');
    }
    return exam;
  } catch (error) {
    console.error('Error fetching exam:', error);
    throw error;
  }
}

async function getUserInfo(email: string) {
  try {
    const userInfo = await GlobalApi.getUserProfile(email);
    if (!userInfo) {
      throw new Error('User not found');
    }
    return userInfo;
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { examId, answers, email } = await request.json();

    if (!email) {
      throw new Error('Email is required');
    }

    // Fetch both exam and user info
    const [exam, userInfo] = await Promise.all([
      fetchExamFromDatabase(examId),
      getUserInfo(email)
    ]);

    let score = 0;
    const questions = exam.questionsAndChoices[0].questionsAndChoices;
    
    questions.forEach((question: any) => {
      const userAnswer = answers[question.question];
      
      if (Array.isArray(userAnswer)) {
        // Handle multiple choice questions
        const correctAnswers = Array.isArray(question.correct_answer) 
          ? question.correct_answer 
          : [question.correct_answer];
        
        if (userAnswer.length === correctAnswers.length && 
            userAnswer.every(answer => correctAnswers.includes(answer))) {
          score += 1;
        }
      } else {
        // Handle single choice questions
        if (userAnswer === question.correct_answer) {
          score += 1;
        }
      }
    });

    const percentage = Math.round((score / questions.length) * 100);
    const passed = percentage >= exam.examScore;

    // Store the exam attempt in the database
    let examAttempt;
    try {
      // Check for existing attempts
      let existingAttempt;
      try {
        existingAttempt = await GlobalApi.getExamAttempts(exam.id, userInfo.id);
      } catch (error) {
        console.error('Error getting existing attempts:', error);
        existingAttempt = null;
      }

      // If there's an existing attempt and it's disabled, reject the submission
      if (existingAttempt?.examIsDisabled) {
        return NextResponse.json(
          { error: 'You have no more attempts left for this exam' },
          { status: 403 }
        );
      }

      // Determine attempts left - if no existing attempt, start with 3
      const attemptsLeft = existingAttempt ? existingAttempt.attemptsLeft : 3;

      examAttempt = await GlobalApi.createExamAttempt({
        exams: {
          connect: {
            id: exam.id
          }
        },
        userInfos: {
          connect: {
            id: userInfo.id
          }
        },
        attemptsLeft,
        examIsDisabled: false
      });

      // If exam failed, decrease attempts
      if (!passed) {
        const newAttemptsLeft = attemptsLeft - 1;
        try {
          // Update attempt count
          await GlobalApi.updateExamAttemptCount({
            id: examAttempt.createExamAttempt.id,
            attemptsLeft: newAttemptsLeft,
            examIsDisabled: newAttemptsLeft <= 0
          });

          // If no attempts left, update enrollment status to Dropped
          if (newAttemptsLeft <= 0) {
            try {
              // Get the course ID from the exam
              const courseSlug = exam.examName.coursesList[0].slug;
              await GlobalApi.updateEnrollmentStatus(email, courseSlug, "Dropped");
            } catch (error) {
              console.error('Error updating enrollment status:', error);
              // Don't throw here, as the attempt update was successful
            }
          }
        } catch (error) {
          console.error('Error updating exam attempt:', error);
          throw error;
        }
      }
    } catch (error) {
      console.error('Error handling exam attempt:', error);
      throw error;
    }

    return NextResponse.json({ 
      percentage, 
      passed,
      email,
      totalQuestions: questions.length,
      correctAnswers: score
    });
  } catch (error) {
    console.error('Error processing exam submission:', error);
    return NextResponse.json(
      { error: 'Error processing exam submission' }, 
      { status: 500 }
    );
  }
}

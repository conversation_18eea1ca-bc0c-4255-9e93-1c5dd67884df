"use client";

import { useUser } from "@clerk/nextjs";
import { useRouter, usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { getUserProfile } from "@/app/_utils/GlobalApi";

export default function ProfileCheck({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const pathname = usePathname();
  const [hasProfile, setHasProfile] = useState<boolean | null>(null);

  useEffect(() => {
    const checkUserProfile = async () => {
      // Don't do anything if we're already on the onboarding page
      if (!user || pathname === '/onboarding') {
        setHasProfile(false);
        return;
      }

      try {
        const profile = await getUserProfile(user.emailAddresses[0]?.emailAddress);
        
        // Check if all required fields are filled
        const isProfileComplete = profile && 
          profile.idNumber && 
          profile.cellphoneNumber && 
          profile.airport && 
          profile.physicalAddress;

        if (isProfileComplete) {
          setHasProfile(true);
        } else {
          console.log(`Profile check: Incomplete profile for ${user.emailAddresses[0]?.emailAddress}`);
          setHasProfile(false);
          // Only redirect if we're not already on the onboarding page
          if (pathname !== '/onboarding') {
            router.replace('/onboarding');
          }
        }
      } catch (error) {
        if (error.message === 'User not found') {
          console.log(`Profile check: No profile found for ${user.emailAddresses[0]?.emailAddress}`);
          setHasProfile(false);
          // Only redirect if we're not already on the onboarding page
          if (pathname !== '/onboarding') {
            router.replace('/onboarding');
          }
        } else {
          console.error('Profile check: Unexpected error:', error);
          setHasProfile(null);
        }
      }
    };

    if (isLoaded && user) {
      checkUserProfile();
    }
  }, [user, isLoaded, router, pathname]);

  // If we're loading or had an error checking the profile
  if (!isLoaded || hasProfile === null) {
    return <div>Loading...</div>;
  }

  // If we're on the onboarding page, always show it
  if (pathname === '/onboarding') {
    return <>{children}</>;
  }

  // If we don't have a complete profile and we're not on onboarding, show nothing
  if (!hasProfile && pathname !== '/onboarding') {
    return null;
  }

  // Otherwise, show the children
  return <>{children}</>;
} 
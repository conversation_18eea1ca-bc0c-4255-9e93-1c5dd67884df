{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.tsx", "src/app/_utils/GlobalApi.tsx", "./declarations.d.ts", "src/app/(dashboard)/_components/CourseList.tsx", "src/app/(dashboard)/course-preview/[courseId]/page.tsx"], "exclude": ["node_modules"]}
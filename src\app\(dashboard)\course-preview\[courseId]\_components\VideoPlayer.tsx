import React, { useRef, useState } from 'react';
import MuxPlayer from "@mux/mux-player-react"; 

type VideoPlayerProps = {
  videoUrl: string;
};

const VideoPlayer: React.FC<VideoPlayerProps> = ({ videoUrl }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  return (
    <div>
      <video className='rounded-lg' ref={videoRef} src={videoUrl} controlsList="nodownload" controls />
    </div>
  );
};

export default VideoPlayer;

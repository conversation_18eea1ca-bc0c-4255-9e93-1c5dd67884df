import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { getUserProfile } from "@/app/_utils/GlobalApi";
import {
  generateCertificatePDF,
  validateExamScore,
  generateCertificateFilename,
} from "@/app/_utils/certificateUtils";

export async function POST(req: NextRequest) {
  try {
    console.log("Received POST request at /api/generate-certificate-html");

    // Get the current authenticated user using Clerk
    const { userId } = await auth();
    if (!userId) {
      console.warn("Unauthorized access attempt - user not logged in");
      return NextResponse.json(
        { error: "Unauthorized - User not logged in" },
        { status: 401 },
      );
    }

    // Parse request body
    const body = await req.json();
    const { courseName, examScore } = body;

    // Validate exam score (must be 80 or above)
    if (!validateExamScore(examScore)) {
      return NextResponse.json(
        { error: "Exam score must be 80 or above to generate certificate" },
        { status: 400 },
      );
    }

    // Get user email from Clerk
    const { clerkClient } = await import("@clerk/backend");
    const user = await clerkClient.users.getUser(userId);
    const userEmail = user.emailAddresses.find(
      (email) => email.id === user.primaryEmailAddressId,
    )?.emailAddress;

    if (!userEmail) {
      console.warn("User email not found");
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 },
      );
    }

    // Fetch complete user profile from our database
    const userProfile = await getUserProfile(userEmail);
    if (!userProfile) {
      console.warn("User profile not found");
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 400 },
      );
    }

    // Prepare user data for certificate generation
    const userData = {
      firstName: user.firstName || userProfile.name || "",
      lastName: user.lastName || userProfile.surname || "",
      idNumber: userProfile.idNumber || "N/A",
    };

    // Generate the certificate PDF
    const pdfBuffer = await generateCertificatePDF(userData);

    // Generate filename
    const filename = generateCertificateFilename(
      userData.firstName,
      userData.lastName,
    );

    // Return the PDF as a response
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error("Error in POST /api/generate-certificate-html:", error);
    return NextResponse.json(
      {
        error: "Failed to generate certificate",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

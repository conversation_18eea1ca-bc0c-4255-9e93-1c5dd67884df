import React, { useState } from 'react';

const CertificateGenerator: React.FC = () => {
  const [studentName, setStudentName] = useState('');
  const [courseName, setCourseName] = useState('');

  const handleGenerateCertificate = async () => {
    const completionDate = new Date().toLocaleDateString();
    const response = await fetch('/api/generate-certificate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ studentName, courseName, completionDate }),
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = 'certificate.pdf';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } else {
      console.error('Failed to generate certificate');
    }
  };

  return (
    <div>
      <input
        type="text"
        placeholder="Student Name"
        value={studentName}
        onChange={(e) => setStudentName(e.target.value)}
      />
      <input
        type="text"
        placeholder="Course Name"
        value={courseName}
        onChange={(e) => setCourseName(e.target.value)}
      />
      <button onClick={handleGenerateCertificate}>Generate Certificate</button>
    </div>
  );
};

export default CertificateGenerator;

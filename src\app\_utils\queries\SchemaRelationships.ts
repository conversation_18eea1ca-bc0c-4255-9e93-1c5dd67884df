import { gql } from 'graphql-request';

export const GET_SCHEMA_RELATIONSHIPS = gql`
  query GetSchemaRelationships {
  userInfos {
    # Basic User Information
    id
    name
    surname
    email
    cellphoneNumber
    idNumber
    physicalAddress

    # Course Enrollments and Progress
    enrolledCourses {
      id
      enrollmentDate
      courseList {
        id
        name
        description
        totalChapters
        duration
        certificateAvailable
        quizesAvailable
        chapter {
          ... on Chapter {
            id
            name
            shortDescription
            video {
              url
            }
            moduleQuizAndChoices # This is a JSON field, we get it as is
          }
        }
      }
    }

    # Exam Attempts and Results
    examAttempts {
      id
      attemptsLeft
      examIsDisabled
      exams {
        id
        examScore
        examDuration
        examDate
        examDescription {
          markdown
        }
        examName {
          name
          isOnLine
        }
        questionsAndChoices # This is likely also a JSON field
      }
    }
  }
}
`;

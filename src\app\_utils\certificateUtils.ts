import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';

export interface CertificateData {
  name: string;
  idNumber: string;
  currentDate: string;
  issuedDate: string;
  datePlusTwoYears: string;
}

export interface UserData {
  firstName: string;
  lastName: string;
  idNumber: number | string;
}

/**
 * Format user name as "<PERSON> <PERSON><PERSON>" (first initial + surname)
 */
export function formatCertificateName(firstName: string, lastName: string): string {
  const firstInitial = firstName.charAt(0).toUpperCase();
  return `${firstInitial} ${lastName}`;
}

/**
 * Generate formatted dates for certificate
 */
export function generateCertificateDates() {
  const currentDate = new Date().toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
  
  const issuedDate = new Date().toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });

  // Calculate date plus two years
  const datePlusTwoYears = new Date();
  datePlusTwoYears.setFullYear(datePlusTwoYears.getFullYear() + 2);
  const formattedDatePlusTwoYears = datePlusTwoYears.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });

  return {
    currentDate,
    issuedDate,
    datePlusTwoYears: formattedDatePlusTwoYears
  };
}

/**
 * Replace placeholders in HTML template with actual data
 */
export function replacePlaceholders(htmlTemplate: string, data: CertificateData): string {
  return htmlTemplate
    .replace(/{{NAME}}/g, data.name)
    .replace(/{{ID_NUMBER}}/g, data.idNumber)
    .replace(/{{CURRENT_DATE}}/g, data.currentDate)
    .replace(/{{ISSUED_DATE}}/g, data.issuedDate)
    .replace(/{{DATE_PLUS_TWO_YEARS}}/g, data.datePlusTwoYears);
}

/**
 * Generate PDF from HTML template using Puppeteer
 */
export async function generateCertificatePDF(userData: UserData): Promise<Buffer> {
  try {
    // Format the name as "J Doe" (first initial + surname)
    const formattedName = formatCertificateName(userData.firstName, userData.lastName);
    
    // Generate dates
    const dates = generateCertificateDates();

    // Read the HTML template
    const templatePath = path.join(process.cwd(), 'public', 'Template_AVSEC_GA_certificate_24_filled_in.html');
    let htmlTemplate = fs.readFileSync(templatePath, 'utf8');

    // Prepare certificate data
    const certificateData: CertificateData = {
      name: formattedName,
      idNumber: userData.idNumber?.toString() || 'N/A',
      currentDate: dates.currentDate,
      issuedDate: dates.issuedDate,
      datePlusTwoYears: dates.datePlusTwoYears
    };

    // Replace placeholders with actual data
    htmlTemplate = replacePlaceholders(htmlTemplate, certificateData);

    // Launch Puppeteer and generate PDF
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Set the HTML content
    await page.setContent(htmlTemplate, {
      waitUntil: 'networkidle0'
    });

    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '0mm',
        right: '0mm',
        bottom: '0mm',
        left: '0mm'
      }
    });

    await browser.close();

    return pdfBuffer;

  } catch (error) {
    console.error('Error generating certificate PDF:', error);
    throw new Error(`Failed to generate certificate: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validate exam score for certificate generation
 */
export function validateExamScore(examScore?: number): boolean {
  return !examScore || examScore >= 80;
}

/**
 * Generate certificate filename
 */
export function generateCertificateFilename(firstName: string, lastName: string): string {
  const formattedName = formatCertificateName(firstName, lastName);
  return `Certificate_${formattedName.replace(/\s+/g, '_')}.pdf`;
}

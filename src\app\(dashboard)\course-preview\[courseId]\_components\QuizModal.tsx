import React, { useState, useEffect } from "react";
import QuizResult from "./QuizResult";

function QuizModal({ selectedQuiz, closeModal }) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [result, setResult] = useState(null);
  const [shuffledQuestions, setShuffledQuestions] = useState([]);

  useEffect(() => {
    // Shuffle the questions array when the component mounts
    const shuffled = [...selectedQuiz[0].questions].sort(() => 0.5 - Math.random());
    setShuffledQuestions(shuffled);
  }, [selectedQuiz]);

  const currentQuestion = shuffledQuestions[currentQuestionIndex];
  const totalQuestions = shuffledQuestions.length;

  if (!currentQuestion) {
    return <div>No questions available.</div>;
  }

  const handleNext = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handlePrev = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleAnswerChange = (choice) => {
    setAnswers({
      ...answers,
      [currentQuestionIndex]: choice,
    });
  };

  const calculateCorrectAnswers = () => {
    let correctCount = 0;

    shuffledQuestions.forEach((question, index) => {
      const correctAnswer = question.answer.replace(/\*\*/g, "");
      const userAnswer = answers[index];

      if (userAnswer === correctAnswer) {
        correctCount += 1;
      }
    });

    return correctCount;
  };

  const handleSubmit = () => {
    const correctAnswers = calculateCorrectAnswers();
    setResult(correctAnswers);
    setSubmitted(true);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-1/2 rounded-lg bg-white p-5 shadow-lg">
        <div className="flex justify-between">
          <h2 className="text-lg font-bold">
            Quiz Questions <span className="text-sm font-normal">({currentQuestionIndex + 1} of {totalQuestions})</span>
          </h2>
          <button onClick={closeModal} className="text-red-500">
            Close
          </button>
        </div>
        {submitted ? (
          <QuizResult
            totalQuestions={totalQuestions}
            correctAnswers={result}
            onClose={closeModal}
          />
        ) : (
          <>
            <div className="mt-4">
              <h3 className="font-bold">{currentQuestion.question}</h3>
              {currentQuestion.choices.map((choice, index) => (
                <div key={index} className="flex items-center space-x-2 mt-2">
                  <input
                    type="radio"
                    id={`choice-${currentQuestionIndex}-${index}`}
                    name={`question-${currentQuestionIndex}`}
                    value={choice}
                    onChange={() => handleAnswerChange(choice)}
                    checked={answers[currentQuestionIndex] === choice}
                  />
                  <label htmlFor={`choice-${currentQuestionIndex}-${index}`}>
                    {choice}
                  </label>
                </div>
              ))}
            </div>
            <div className="mt-4 flex justify-between">
              <button
                className="px-4 py-2 bg-blue-500 text-white rounded"
                onClick={handlePrev}
                disabled={currentQuestionIndex === 0}
              >
                Prev
              </button>
              {currentQuestionIndex < totalQuestions - 1 ? (
                <button
                  className="px-4 py-2 bg-blue-500 text-white rounded"
                  onClick={handleNext}
                  disabled={!answers[currentQuestionIndex]}
                >
                  Next
                </button>
              ) : (
                <button
                  className="px-4 py-2 bg-green-500 text-white rounded"
                  onClick={handleSubmit}
                  disabled={!answers[currentQuestionIndex]}
                >
                  Submit
                </button>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default QuizModal;

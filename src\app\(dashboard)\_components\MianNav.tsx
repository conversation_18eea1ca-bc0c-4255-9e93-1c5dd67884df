"use client";

import React from "react";
import { Search, BellDot } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { UserButton, useUser } from "@clerk/nextjs";
import Link from "next/link";
import ThemeToggler from "./ThemeToggler";

function MainNav() {
  const { user, isLoaded } = useUser();

  return (
    <div className="flex items-center justify-between bg-white p-4">
      <div className="flex gap-2 rounded-md p-2 ">
        <Search className="h-5 w-5 text-white" />
        <input
          type="text"
          placeholder="Search"
          className="rounded-sm bg-white p-1 outline-none"
        />
      </div>
      <div>
        <ThemeToggler/>
      </div>
      <div className="flex items-center gap-2">
        {isLoaded && user ? (
          <UserButton afterSignOutUrl="/courses" />
        ) : (
          <Link href="/sign-in">
            <Button className="hover:bg-yellow">Get started</Button>
          </Link>
        )}
      </div>
    </div>
  );
}

export default MainNav;

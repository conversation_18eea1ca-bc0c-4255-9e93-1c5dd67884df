import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/clerk-sdk-node';
import nodemailer from 'nodemailer';
import type { NextRequest } from 'next/server';
import GlobalApi, { airportDisplayNames } from '@/app/_utils/GlobalApi';

export async function POST(req: NextRequest) {
  try {
    console.log('Received POST request at /api/request-certificate');

    // Hardcoded certificate name
    const certificateName = 'Aviation Security(AVESC) General Awareness';
    console.log(`Using hardcoded certificate name: ${certificateName}`);

    // Get the current authenticated user using Clerk
    const { userId } = await auth();
    if (!userId) {
      console.warn('Unauthorized access attempt - user not logged in');
      return NextResponse.json({ error: 'Unauthorized - User not logged in' }, { status: 401 });
    }

    console.log(`Authenticated user with userId: ${userId}`);

    // Fetch user details with Clerk API
    const user = await clerkClient.users.getUser(userId);
    const userName = user.firstName || 'User';
    const userEmail = user.emailAddresses.find(
      (email) => email.id === user.primaryEmailAddressId
    )?.emailAddress;

    console.log(`Fetched user details - Name: ${userName}, Email: ${userEmail}`);

    if (!userEmail) {
      console.warn('User email not found');
      return NextResponse.json({ error: 'User email not found' }, { status: 400 });
    }

    // Fetch complete user profile from our database
    const userProfile = await GlobalApi.getUserProfile(userEmail);
    if (!userProfile) {
      console.warn('User profile not found');
      return NextResponse.json({ error: 'User profile not found' }, { status: 400 });
    }

    // Configure Nodemailer transport
    const transporter = nodemailer.createTransport({
      host: 'mail.globalsafeskyz.co.za',
      port: 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
      tls: {
        // Do not fail on invalid certificates
        rejectUnauthorized: false
      }
    });

    // Admin email address
    const adminEmail = process.env.EMAIL_USER;
    console.log(`Configured Nodemailer - Admin email: ${adminEmail}`);

    // Format the airport name if it exists
    const airportDisplayName = userProfile.airport ? (airportDisplayNames[userProfile.airport] || userProfile.airport) : 'Not specified';

    // Email content to be sent to the admin with complete user information
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: adminEmail,
      subject: 'Certificate Request',
      text: `
        Certificate Request Details:
        
        Certificate: ${certificateName}
        
        User Information:
        Full Name: ${userProfile.name} ${userProfile.surname}
        Email: ${userProfile.email}
        Cell Phone: ${userProfile.cellphoneNumber}
        ID Number: ${userProfile.idNumber}
        Airport: ${airportDisplayName}
        Physical Address: ${userProfile.physicalAddress}
        Company: ${userProfile.companyName || 'Not specified'}
      `,
      html: `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
        <h2 style="color: #4CAF50;">Certificate Request Details</h2>
        
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="color: #2196F3; margin-top: 0;">Requested Certificate</h3>
          <p style="margin: 0;"><strong>${certificateName}</strong></p>
        </div>

        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px;">
          <h3 style="color: #2196F3; margin-top: 0;">User Information</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0;"><strong>Full Name:</strong></td>
              <td style="padding: 8px 0;">${userProfile.name} ${userProfile.surname}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0;"><strong>Email:</strong></td>
              <td style="padding: 8px 0;"><a href="mailto:${userProfile.email}" style="color: #1a73e8; text-decoration: none;">${userProfile.email}</a></td>
            </tr>
            <tr>
              <td style="padding: 8px 0;"><strong>Cell Phone:</strong></td>
              <td style="padding: 8px 0;">${userProfile.cellphoneNumber}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0;"><strong>ID Number:</strong></td>
              <td style="padding: 8px 0;">${userProfile.idNumber}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0;"><strong>Airport:</strong></td>
              <td style="padding: 8px 0;">${airportDisplayName}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0;"><strong>Physical Address:</strong></td>
              <td style="padding: 8px 0;">${userProfile.physicalAddress}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0;"><strong>Company:</strong></td>
              <td style="padding: 8px 0;">${userProfile.companyName || 'Not specified'}</td>
            </tr>
          </table>
        </div>

        <p style="margin-top: 20px; font-size: 14px; color: #666;">
          This is an automated notification. Please process this certificate request accordingly.
        </p>
      </div>
    `,
    };

    // Send the email
    console.log('Sending email...');
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', info);

    // Respond with success message
    return NextResponse.json({ message: 'Request sent to admin successfully', info });
  } catch (error) {
    console.error('Error in POST /request-certificate', error);
    return NextResponse.json({ error: 'Failed to send request' }, { status: 500 });
  }
}

import { NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import nodemailer from 'nodemailer';
import type { NextRequest } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const { courseName } = await req.json();
    if (!courseName) {
      return NextResponse.json({ error: 'Course name is required' }, { status: 400 });
    }

    // Get authenticated user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized - User not logged in' }, { status: 401 });
    }

    try {
      // Fetch user details using Clerk API
      const client = await clerkClient();
      const user = await client.users.getUser(userId);
      const userName = user.firstName || 'User';
      const userEmail = user.emailAddresses.find(
        (email) => email.id === user.primaryEmailAddressId
      )?.emailAddress;

      if (!userEmail) {
        return NextResponse.json({ error: 'User email not found' }, { status: 400 });
      }

      // Configure nodemailer transport
      const transporter = nodemailer.createTransport({
        host: 'mail.globalsafeskyz.co.za',
        port: 587,
        secure: false, // Use TLS
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS,
        },
      });

      // Email content
      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: userEmail,
        subject: `Enrollment Confirmation for ${courseName}`,
        text: `Hello ${userName},\n\nYou have successfully enrolled in the course: ${courseName}.`,
        html: `
          <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Course Enrollment Confirmation</h2>
            <p>Hello ${userName},</p>
            <p>You have successfully enrolled in the course:</p>
            <h3 style="color: #4CAF50; margin: 20px 0;">${courseName}</h3>
            <p>You can now access your course materials through your dashboard.</p>
            <p style="margin-top: 30px;">Best regards,<br>Global Safe Skyz Team</p>
          </div>
        `,
      };

      // Send the email
      const info = await transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', info);

      // Respond with success message
      return NextResponse.json({ 
        message: 'Email sent successfully', 
        info: {
          messageId: info.messageId,
          recipient: userEmail
        }
      });

    } catch (clerkError) {
      console.error('Error fetching user details from Clerk:', clerkError);
      return NextResponse.json({ 
        error: 'Failed to fetch user details',
        details: clerkError.message 
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in POST /send-enrollment-email:', error);
    return NextResponse.json({ 
      error: 'Failed to send enrollment email',
      details: error.message 
    }, { status: 500 });
  }
}

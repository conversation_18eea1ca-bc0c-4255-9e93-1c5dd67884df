"use client";

import React, { useEffect } from "react";
import { useUser, useClerk } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import MyProfile from "../../(dashboard)/profile/_components/MyProfile";
import { getUserProfile } from "@/app/_utils/GlobalApi";

export default function OnboardingPage() {
  const router = useRouter();
  const { user, isLoaded } = useUser();
  const { signOut } = useClerk();

  useEffect(() => {
    const checkUserProfile = async () => {
      if (!user) return;

      try {
        const profile = await getUserProfile(user.emailAddresses[0]?.emailAddress);
        // Check if profile exists AND is complete
        const isProfileComplete = profile && 
          profile.idNumber && 
          profile.cellphoneNumber && 
          profile.airport && 
          profile.physicalAddress;

        if (isProfileComplete) {
          // Only redirect to dashboard if profile is complete
          router.push("/dashboard");
        }
      } catch (error) {
        console.error("Error checking user profile:", error);
      }
    };

    if (isLoaded) {
      checkUserProfile();
    }
  }, [user, isLoaded, router]);

  if (!isLoaded || !user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto max-w-3xl px-4 py-8">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-primary">Complete Your Profile</h1>
          <p className="text-gray-600">Please fill in your details to continue</p>
        </div>
        <button
          onClick={() => signOut()}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          Sign Out
        </button>
      </div>
      <MyProfile isOnboarding={true} />
    </div>
  );
} 
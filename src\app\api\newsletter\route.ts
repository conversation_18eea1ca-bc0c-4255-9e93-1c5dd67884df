import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: Request) {
  try {
    const { name, email } = await request.json();

    // Check for missing name or email
    if (!name || !email) {
      console.warn('Missing required fields:', { name, email });
      return NextResponse.json(
        { message: 'Name and email are required.' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { message: 'Invalid email format.' },
        { status: 400 }
      );
    }

    // Ensure environment variables are set
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      throw new Error('EMAIL_USER and EMAIL_PASS must be set in the environment.');
    }

    // Configure Nodemailer transport
    const transporter = nodemailer.createTransport({
      host: 'mail.globalsafeskyz.co.za',
      port: 587,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Verify transporter
    await transporter.verify();
    console.log('Transporter verified successfully.');

    // Admin email address
    const adminEmail = process.env.EMAIL_USER;

    // Send email to admin
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: adminEmail,
      subject: 'New Newsletter Subscription',
      text: `New subscriber details:\nName: ${name}\nEmail: ${email}`,
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
          <h2 style="color: #4CAF50;">New Newsletter Subscriber</h2>
          <p><strong>Name:</strong> ${name}</p>
          <p><strong>Email:</strong> ${email}</p>
        </div>
      `,
    });

    // Send confirmation to subscriber
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'Welcome to GlobalSafeSkyz Newsletter',
      text: `Dear ${name},\n\nThank you for subscribing to our newsletter! We're excited to have you on board.\n\nBest regards,\nGlobalSafeSkyz Team`,
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
          <h2 style="color: #4CAF50;">Welcome to GlobalSafeSkyz Newsletter!</h2>
          <p>Dear ${name},</p>
          <p>Thank you for subscribing to our newsletter! We're excited to have you on board.</p>
          <p>Best regards,<br>GlobalSafeSkyz Team</p>
        </div>
      `,
    });

    // Respond with success message
    return NextResponse.json(
      { message: 'Successfully subscribed to newsletter' },
      { status: 200 }
    );
  } catch (error) {
    // Log error and respond with failure message
    console.error('Newsletter subscription error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error processing subscription';
    return NextResponse.json(
      { message: errorMessage },
      { status: 500 }
    );
  }
}

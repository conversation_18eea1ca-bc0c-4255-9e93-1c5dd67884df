import React, { useState } from "react";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import CourseEnrollmentForm from "./CourseEnrollmentForm";
import Image from "next/image";

function CourseModal({ course, onClose, onEnroll, userName }) {
  const [isCodeValid, setIsCodeValid] = useState(false);

  const handleCodeValid = (isValid) => {
    setIsCodeValid(isValid);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-1/2 rounded-lg bg-white p-5 shadow-lg">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-bold text-black">{course.name}</h2>
          <button onClick={onClose} className="text-red-500">
            Close
          </button>
        </div>

        {course.image && (
          <div className="mt-4">
            <Image
              src={course?.banner?.url || "https://via.placeholder.com/500x150"}
              alt={course.name}
              className="rounded-lg"
              width={500}
              height={150}
              style={{ width: '100%', height: 'auto' }}
            />
          </div>
        )}

        <div className="mt-4 text-black">
          <p><strong>Student Name:</strong> {userName}</p>
        </div>

        <Accordion type="single" collapsible>
          <AccordionItem value="item-1">
            <AccordionTrigger className="bg-gray-200 text-black rounded-lg mb-2 p-2 hover:bg-gray-300">
              Course Details
            </AccordionTrigger>
            <AccordionContent className="bg-gray-100 text-black p-4 border-t border-gray-300">
              <p>
                <strong>Description:</strong> {course.description}
              </p>
              <p>
                <strong>Duration:</strong> {course.duration}
              </p>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <div className="mt-6">
          <CourseEnrollmentForm
            courseSlug={course.slug}
            onEnroll={onEnroll}
            onCodeValid={handleCodeValid}
          />
        </div>
      </div>
      <ToastContainer />
    </div>
  );
}

export default CourseModal;
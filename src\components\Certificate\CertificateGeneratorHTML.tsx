"use client";

import React, { useState } from 'react';
import { useUser } from '@clerk/nextjs';

interface CertificateGeneratorHTMLProps {
  courseName?: string;
  examScore?: number;
  onSuccess?: (certificateUrl: string) => void;
  onError?: (error: string) => void;
}

const CertificateGeneratorHTML: React.FC<CertificateGeneratorHTMLProps> = ({
  courseName = "Aviation Security (AVSEC) General Awareness",
  examScore,
  onSuccess,
  onError
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const { user, isLoaded } = useUser();

  const handleGenerateCertificate = async () => {
    if (!isLoaded || !user) {
      const errorMsg = 'User not authenticated';
      console.error(errorMsg);
      onError?.(errorMsg);
      return;
    }

    setIsGenerating(true);

    try {
      const response = await fetch('/api/generate-certificate-html', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          courseName,
          examScore,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate certificate');
      }

      // Get the PDF as a blob
      const blob = await response.blob();

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create a download link and trigger download
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `Certificate_${user.firstName}_${user.lastName}.pdf`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Call success callback
      onSuccess?.(url);

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Failed to generate certificate';
      console.error('Error generating certificate:', error);
      onError?.(errorMsg);
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePreviewCertificate = async () => {
    if (!isLoaded || !user) {
      const errorMsg = 'User not authenticated';
      console.error(errorMsg);
      onError?.(errorMsg);
      return;
    }

    setIsGenerating(true);

    try {
      const response = await fetch('/api/generate-certificate-html', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          courseName,
          examScore,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate certificate');
      }

      // Get the PDF as a blob
      const blob = await response.blob();

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Open the PDF in a new tab for preview
      window.open(url, '_blank');

      // Call success callback
      onSuccess?.(url);

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Failed to generate certificate';
      console.error('Error generating certificate:', error);
      onError?.(errorMsg);
    } finally {
      setIsGenerating(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-red-500">Please sign in to generate certificate</div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Certificate Generator
        </h3>
        <p className="text-sm text-gray-600">
          Generate your {courseName} certificate
        </p>
      </div>

      <div className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-md">
          <div className="text-sm text-gray-600 mb-2">Student Information:</div>
          <div className="text-sm">
            <div><strong>Name:</strong> {user.firstName?.charAt(0).toUpperCase()} {user.lastName}</div>
            <div><strong>Course:</strong> {courseName}</div>
            {examScore && <div><strong>Score:</strong> {examScore}%</div>}
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={handlePreviewCertificate}
            disabled={isGenerating}
            className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isGenerating ? (
              <>
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating...
              </>
            ) : (
              <>
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                Preview
              </>
            )}
          </button>

          <button
            onClick={handleGenerateCertificate}
            disabled={isGenerating}
            className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isGenerating ? (
              <>
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating...
              </>
            ) : (
              <>
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Download
              </>
            )}
          </button>
        </div>

        {examScore && examScore < 80 && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <div className="text-red-800 text-sm">
              <strong>Note:</strong> A minimum score of 80% is required to generate a certificate. 
              Your current score is {examScore}%.
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CertificateGeneratorHTML;

import RelatedPost from "@/components/Blog/RelatedPost";
import Image from "next/image";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faCheck } from "@fortawesome/free-solid-svg-icons";
import { Metadata } from "next";
import Breadcrumb from "@/components/Common/Breadcrumb";
import Link from "next/link";
import Video from "@/components/Video";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";

export const metadata: Metadata = {
  title: "Courses Details | Insights from GlobalSafeSkyz",
  description:
    "Explore in-depth articles and insights on aviation security from GlobalSafeSkyz.",
  // other metadata
};

const CourseDetailsPage = () => {
  return (
    <>
      <Breadcrumb pageName="Course Details" description="" />

      <section className="overflow-hidden pb-[120px] pt-[10px]">
        <div className="container">
          <div className="-mx-4 flex flex-wrap">
            <div className="w-full px-4 lg:w-8/12">
              <div>
                <div>
                  <div className="mb-3 w-full overflow-hidden rounded">
                    <div className="relative aspect-[97/60] w-full sm:aspect-[97/44]">
                      <Video />
                    </div>
                  </div>
                  <p className="mb-8 text-base font-medium leading-relaxed text-body-color sm:text-lg sm:leading-relaxed lg:text-base lg:leading-relaxed xl:text-lg xl:leading-relaxed">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
                    do eiusmod tempor incididunt ut labore et dolore magna
                    aliqua. Quis enim lobortis scelerisque fermentum. Neque
                    sodales ut etiam sit amet. Ligula ullamcorper
                    <strong className="text-primary dark:text-white">
                      {" "}
                      malesuada{" "}
                    </strong>
                    proin libero nunc consequat interdum varius. Quam
                    pellentesque nec nam aliquam sem et tortor consequat.
                    Pellentesque adipiscing commodo elit at imperdiet.
                  </p>
                </div>
              </div>
            </div>
            <div className="w-full px-4 lg:w-4/12 rounded-mb">
              <Progress className="bg-red mb-2" value={10} />
              <div className="mb-10 justify-center rounded-sm bg-white shadow-three dark:bg-gray-dark dark:shadow-none">
                <h3 className="border-b border-body-color border-opacity-10 px-8 py-4 text-lg font-semibold text-black dark:border-white dark:border-opacity-10 dark:text-white">
                  Course Modules
                </h3>

                <Link href={""}>
                  <div className="flex items-center border-b border-body-color border-opacity-80 bg-green-700 p-2">
                    <div>
                      <FontAwesomeIcon
                        icon={faCheck}
                        className="m-1 mr-4 w-4 "
                        style={{ color: "white" }}
                      />
                    </div>
                    <div className="items-center ">
                      <RelatedPost
                        title="Introduction : Aviation Security 101"
                        image="/images/courses/modules/AVSEC GA M5.jpg"
                        slug="#"
                      />
                    </div>
                  </div>
                </Link>

                <Link href={""}>
                  <div className="flex cursor-wait   items-center border-b border-body-color border-opacity-80 bg-green-700 p-2">
                    <div>
                      <FontAwesomeIcon
                        icon={faCheck}
                        className="m-1 mr-4 w-4 "
                        style={{ color: "white" }}
                      />
                    </div>
                    <div className="items-center dark:border-white dark:border-opacity-10">
                      <RelatedPost
                        title="Module 2: Break room "
                        image="/images/courses/modules/AVSEC GA M5.jpg"
                        slug="#"
                      />
                    </div>
                  </div>
                </Link>

                <Link href={""}>
                  <div className="flex cursor-wait disabled  items-center border-b border-body-color border-opacity-80 bg-slate-900 p-2">
                    <div>
                      <FontAwesomeIcon
                        icon={faEye}
                        className="m-1 mr-4 w-4 "
                        style={{ color: "white" }}
                      />
                    </div>
                    <div className="items-center dark:border-white dark:border-opacity-10">
                      <RelatedPost
                        title="Module 3 : Aviation Security "
                        image="/images/courses/modules/AVSEC GA M5.jpg"
                        slug="#"
                      />
                    </div>
                  </div>
                </Link>

                <Link href={""}>
                  <div className="flex cursor-wait disabled  items-center border-b border-body-color border-opacity-80 bg-slate-900 p-2">
                    <div>
                      <FontAwesomeIcon
                        icon={faEye}
                        className="m-1 mr-4 w-4 "
                        style={{ color: "white" }}
                      />
                    </div>
                    <div className="items-center dark:border-white dark:border-opacity-10">
                      <RelatedPost
                        title="Module 4 : Airport security  "
                        image="/images/courses/modules/AVSEC GA M5.jpg"
                        slug="#"
                      />
                    </div>
                  </div>
                </Link>
                <Link href={""}>
                  <div className="flex cursor-wait disabled  items-center border-b border-body-color border-opacity-80 bg-slate-900 p-2">
                    <div>
                      <FontAwesomeIcon
                        icon={faEye}
                        className="m-1 mr-4 w-4 "
                        style={{ color: "white" }}
                      />
                    </div>
                    <div className="items-center dark:border-white dark:border-opacity-10">
                      <RelatedPost
                        title="Module 5 : Why Airport security is important "
                        image="/images/courses/modules/AVSEC GA M5.jpg"
                        slug="#"
                      />
                    </div>
                  </div>
                </Link>
                <Link href={""}>
                  <div className="flex cursor-wait disabled  items-center border-b border-body-color border-opacity-80 bg-slate-900 p-2">
                    <div>
                      <FontAwesomeIcon
                        icon={faEye}
                        className="m-1 mr-4 w-4 "
                        style={{ color: "white" }}
                      />
                    </div>
                    <div className="items-center dark:border-white dark:border-opacity-10">
                      <RelatedPost
                        title="Module 6 : Aviation theats "
                        image="/images/courses/modules/AVSEC GA M5.jpg"
                        slug="#"
                      />
                    </div>
                  </div>
                </Link>
                <Link href={""}>
                  <div className="flex cursor-wait disabled  items-center border-b border-body-color border-opacity-80 bg-slate-900 p-2">
                    <div>
                      <FontAwesomeIcon
                        icon={faEye}
                        className="m-1 mr-4 w-4 "
                        style={{ color: "white" }}
                      />
                    </div>
                    <div className="items-center dark:border-white dark:border-opacity-10">
                      <RelatedPost
                        title="Module 7 : Bomb threats "
                        image="/images/courses/modules/AVSEC GA M5.jpg"
                        slug="#"
                      />
                    </div>
                  </div>
                </Link>



              </div>

            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default CourseDetailsPage;

import Image from "next/image";
import React from "react";
import Link from "next/link";
import { Award,NotebookPen  } from 'lucide-react';

function CourseItem({ course }) {
  return (
    <div className="cursor-pointer rounded-md border hover:shadow-md hover:shadow-purple-300">
      <div>
        <Image
          src={course?.banner?.url}
          width={500}
          height={150}
          alt="banner"
          className="h-[130px] rounded-t-md object-cover"
        />
        <span className="absolute left-3 top-3 z-20 inline-flex items-center justify-center rounded-full bg-yellow px-4 py-2 text-sm font-semibold capitalize text-white">
          {course?.free ? "Free" : "Paid"}
        </span>
      </div>

      <div className="flex flex-col gap-1 p-2">
          <h2 className="cursor-pointer font-medium">{course.name}</h2>
        <h2 className="text-[12px] text-gray-400">{course.author}</h2>
        {course?.chapter?.length === 0 ? (
          <div className="flex gap-2">
            <Image src="/youtube.png" alt="youtube" width={20} height={20} />
            <h2 className="text-[14px] text-gray-400">Watch On Youtube</h2>
          </div>
        ) : (
          <div className="flex gap-1 justify-between ">
            <div className="flex gap-1">
            <Image src="/chapter.png" alt="chapter" width={20} height={20} />
            <h2 className="text-[14px] text-gray-400">
              {course?.chapter?.length}{" "}
              {course?.chapter?.length === 1 ? "Module" : "Modules"}
            </h2>
            </div>
            <div className="flex">
            {course?.certificateAvailable ? <Award className=" text-gray-400" /> : null}
            {course?.quizesAvailable ? <NotebookPen  className=" text-gray-400" />: null}
            </div>
          </div>
          
        )}
      </div>
    </div>
  );
}

export default CourseItem;

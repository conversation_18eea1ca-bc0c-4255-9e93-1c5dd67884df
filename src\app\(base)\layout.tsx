"use client";
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import Footer from "@/components/Footer";
import Header from "@/components/Header";
import ScrollToTop from "@/components/ScrollToTop";
import { Inter } from "next/font/google";
import "node_modules/react-modal-video/css/modal-video.css";
import "../../styles/index.css";
import { Providers } from '../providers';
import "react-toastify/dist/ReactToastify.css";
import { ToastContainer } from "react-toastify";

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html suppressHydrationWarning lang="en">
        <head />
        <body className={`bg-[#FCFCFC] dark:bg-black ${inter.className}`}>
          <Providers>
            <Header />
            <ToastContainer position="top-right" autoClose={5000} hideProgressBar={false} />
            {children}
            <Footer />
            <ScrollToTop />
          </Providers>
        </body>
      </html>
    </ClerkProvider>
  );
}

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Signed<PERSON>n, SignedOut, UserButton } from '@clerk/nextjs'
import { Providers } from "../providers";
import MianNav from "./_components/MianNav";
import "../../styles/index.css";
import { TimerProvider } from './_components/TimerContext';


export const metadata = {
  title: "Globalsafeskyz | Course",
  description: "Generated by Next.js",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
    <html lang="en">
      <body className="bg-gray-100">
        <Providers>
        <TimerProvider>
          <div>
            <div className="  ">
          
            {children}
            </div>
          </div>
          </TimerProvider>
        </Providers>
      </body>
    </html>
    </ClerkProvider>
  );
}

// @ts-ignore
import { gql, request, GraphQLClient } from "graphql-request";

type Banner = {
  url: string;
};

type Video = {
  url: string;
};

type Chapter = {
  id: string;
  name: string;
  video: Video;
  shortDescription?: string;
};

type Course = {
  author: string;
  banner: Banner;
  chapter: Chapter[];
  description: string;
  free: boolean;
  id: string;
  name: string;
  slug: string;
  tag: string;
  totalChapters: number;
};

type CourseListResponse = {
  courseList: Course[];
};

type UserInfo = {
  id: string;
  name: string;
  surname: string;
  email: string;
  cellphoneNumber: number;
  idNumber: number;
  airport: string;
  physicalAddress: string;
  companyName?: string;
};

const MASTER_URL = process.env.NEXT_PUBLIC_MASTER_URL;
const HYGRAPH_API_KEY = process.env.NEXT_PUBLIC_HYGRAPH_API_KEY;

const graphQLClient = new GraphQLClient(MASTER_URL, {
  headers: {
    authorization: `Bearer ${HYGRAPH_API_KEY}`,
  },
});

const getAllCourseList = async () => {
  const query = gql`
    query MyQuery {
      coursesLists {
        name
        id
        free
        description
        banner {
          url
        }
        chapter {
          ... on Chapter {
            id
            video {
              url
            }
            name
            shortDescription
          }
        }
        totalChapters
        author
        slug
        certificateAvailable
        quizesAvailable
      
        duration
      }
    }
  `;

  const result = await request(MASTER_URL, query);
  return result;
};

const getSideBanner = async () => {
  const query = gql`
    query GetSideBanner {
      sideBanners {
        id
        name
        banner {
          id
          url
        }
        url
      }
    }
  `;
  const result = await request(MASTER_URL, query);
  return result;
};

const getCourseById = async (courseId: string) => {
  const query = gql`
    query MyQuery($slug: String!) {
      coursesLists(where: { slug: $slug }) {
        author
        banner {
          url
        }
        chapter {
          ... on Chapter {
            id
            name
            video {
              url
            }
            shortDescription
            moduleQuizAndChoices
          }
        }

        id
        name
      }
    }
  `;
  const variables = { slug: courseId };
  try {
    const result = await request(MASTER_URL, query, variables);
    console.log("GraphQL Response:", result);
    return result;
  } catch (error) {
    console.error("GraphQL Error:", error);
    throw error;
  }
};

// Refined enrollToCourse function with error handling
const enrollToCourse = async (courseId, email) => {
  const query = gql`
    mutation EnrollToCourse($courseId: String!, $email: String!) {
      createUserEnrollment(
        data: { 
          coursesList: { connect: { slug: $courseId } },
          userInfo: { connect: { email: $email } },
          enrollmentDate: "${new Date().toISOString()}",
          enrolledStatus: Active
        }
      ) {
        id
      }
      publishManyUserEnrollmentsConnection {
        edges {
          node {
            id
          }
        }
      }
    }
  `;

  const variables = { courseId, email };

  try {
    const result = await request(MASTER_URL, query, variables);
    return result;
  } catch (error) {
    console.error("Error during enrollment:", error);
    throw new Error("An error occurred during enrollment. Please try again later.");
  }
};

const checkUserEnrolledToCourse = async (courseId, email) => {
  const query = gql`
    query MyQuery {
      userEnrollCourses(where: { courseId: "${courseId}", userEmail: "${email}" }) {
        id
      }
    }
  `;
  const result = await request(MASTER_URL, query);
  return result;
};

const getUserEnrolledCourseDetails = async (id, email) => {
  const query = gql`
    query MyQuery {
      userEnrollCourses(where: {id: "${id}", userEmail: "${email}"}) {
        courseId
        id
        userEmail
        chaptersCompleted {
          ... on CompletedChapter {
            id
            chapterId
          }
        }
        coursesList {
          author
          banner {
            url
          }
          chapter(first: 50) {
            ... on Chapter {
              id
              name
              video {
                url
              }
            }
          }
          description
          free
          id
          name
          slug
          totalChapters
        }
      }
    }
  `;
  const result = await request(MASTER_URL, query);
  return result;
};

const markChapterCompleted = async (enrollId, chapterId) => {
  const query = gql`
    mutation MyMutation {
      updateUserEnrollCourse(
        data: { completedChapter: { create: { CompletedChapter: { data: { chapterId: "${chapterId}" } } } }
        where: { id: "${enrollId}" }
      ) {
        id
      }
      publishUserEnrollCourse(where: { id: "${enrollId}" }) {
        id
      }
    }
  `;
  const result = await request(MASTER_URL, query);
  return result;
};

const getUserAllEnrolledCourseList = async (email) => {
  const query = gql`
    query MyQuery {
      userEnrollCourses(where: { userEmail: "${email}" }) {
        completedChapter {
          ... on CompletedChapter {
            id
            chapterId
          }
        }
        courseId
        courseList {
          name
          id
          totalChapters
          slug
          sourceCode
          free
          description
          demoUrl
          chapter(first: 50) {
            ... on Chapter {
              id
              name
            }
          }
          author
          banner {
            url
          }
        }
      }
    }
  `;

  const result = await request(MASTER_URL, query);
  return result;
};

const addNewMember = async (email, paymentId) => {
  const query = gql`
    mutation MyMutation {
      createMembership(data: { active: true, email: "${email}", paymentId: "${paymentId}" }) {
        id
      }
      publishManyMemberships(to: PUBLISHED) {
        count
      }
    }
  `;
  const result = await request(MASTER_URL, query);
  return result;
};

const checkForMembership = async (email) => {
  const query = gql`
    query MyQuery {
      memberships(where: { email: "${email}" }) {
        email
        id
        paymentId
        createdAt
      }
    }
  `;
  const result = await request(MASTER_URL, query);
  return result;
};

export const createUserProfile = async (userInfo: Omit<UserInfo, "id">) => {
  const query = gql`
    mutation CreateUserProfile(
      $name: String!
      $surname: String!
      $email: String!
      $cellphoneNumber: Int!
      $idNumber: Int!
      $airport: AirportsInSa
      $physicalAddress: String!
      $companyName: String
    ) {
      createUserInfo(
        data: {
          name: $name
          surname: $surname
          email: $email
          cellphoneNumber: $cellphoneNumber
          idNumber: $idNumber
          airport: $airport
          physicalAddress: $physicalAddress
          companyName: $companyName
        }
      ) {
        id
      }
    }
  `;

  try {
    const variables = {
      name: userInfo.name,
      surname: userInfo.surname,
      email: userInfo.email,
      cellphoneNumber: userInfo.cellphoneNumber,
      idNumber: userInfo.idNumber,
      airport: userInfo.airport || null,
      physicalAddress: userInfo.physicalAddress,
      companyName: userInfo.companyName || null,
    };

    const result = await graphQLClient.request(query, variables);
    const userId = result.createUserInfo.id;
    await publishUserInfo(userId);
    
    return result.createUserInfo;
  } catch (error) {
    throw error;
  }
};

export const updateUserProfile = async (userInfo) => {
  const query = gql`
    mutation UpdateUserProfile(
      $id: ID!
      $name: String!
      $surname: String!
      $email: String!
      $cellphoneNumber: Int!
      $idNumber: Int!
      $airport: AirportsInSa
      $physicalAddress: String!
      $companyName: String
    ) {
      updateUserInfo(
        where: { id: $id }
        data: {
          name: $name
          surname: $surname
          email: $email
          cellphoneNumber: $cellphoneNumber
          idNumber: $idNumber
          airport: $airport
          physicalAddress: $physicalAddress
          companyName: $companyName
        }
      ) {
        id
      }
    }
  `;

  try {
    const variables = {
      id: userInfo.id,
      name: userInfo.name,
      surname: userInfo.surname,
      email: userInfo.email,
      cellphoneNumber: userInfo.cellphoneNumber,
      idNumber: userInfo.idNumber,
      airport: userInfo.airport || null,
      physicalAddress: userInfo.physicalAddress,
      companyName: userInfo.companyName || null,
    };

    const result = await graphQLClient.request(query, variables);
    const userId = result.updateUserInfo.id;
    await publishUserInfo(userId);
    return result;
  } catch (error) {
    console.error("GraphQL error:", error.response.errors);
    throw new Error(`GraphQL error: ${error.message}`);
  }
};

const publishUserInfo = async (id: string) => {
  const mutation = gql`
    mutation publishUserInfo($id: ID!) {
      publishUserInfo(where: { id: $id }) {
        id
        name
        surname
        email
        cellphoneNumber
        idNumber
        physicalAddress
      }
    }
  `;

  const variables = { id };

  try {
    const result = await graphQLClient.request(mutation, variables);
    return result;
  } catch (error) {
    console.error("An error occurred while publishing user info");
    throw new Error("An error occurred while publishing user info");
  }
};

export const getUserProfile = async (email: string) => {
  if (!email) {
    console.error('getUserProfile called with no email');
    throw new Error('Email is required');
  }

  const query = gql`
    query GetUserProfile($email: String!) {
      userInfo(where: { email: $email }) {
        id
        name
        surname
        email
        cellphoneNumber
        idNumber
        airport
        physicalAddress
        companyName
      }
    }
  `;

  try {
    const result = await graphQLClient.request(query, { email });
    
    if (!result.userInfo) {
      console.log(`No user profile found for email: ${email}`);
      throw new Error("User not found");
    }
    return result.userInfo;
  } catch (error) {
    console.error('GraphQL error in getUserProfile:', error);
    throw error;
  }
};

const getAllExamList = async () => {
  const query = gql`
    query MyQuery {
      exams(where: { slug: "aviation-security-avesc-general-awareness" }) {
        id
        examDuration
        questionsAndChoices
        examScore
        examName {
          name
          isOnLine
        }
        examDate
        examDescription {
          markdown
        }
      }
    }
  `;
  try {
    const result = await request(MASTER_URL, query);
    return result;
  } catch (error) {
    console.error("An error occurred while fetching exam list");
    throw new Error("An error occurred while fetching exam list");
  }
};

const getTestimonials = async () => {
  const query = gql`
    query MyQuery {
      testimonials(where: { accepeted: true }) {
        name
        designation
        image {
          url
        }
        star
        content
        id
      }
    }
  `;
  try {
    const result = await request(MASTER_URL, query);
    console.log("GraphQL Response:", result); // Add this log to see the response
    return result;
  } catch (error) {
    console.error("GraphQL Error:", error);
    throw error;
  }
};

// Define your GraphQL query
const GET_CERTIFICATE_DATA = gql`
  query GetCertificateData($slug: String!, $email: String!) {
    certificates(where: { course: { slug: $slug }, user: { email: $email } }) {
      id
      issueDate
      course {
        name
        id
        slug
      }
      user {
        name
        email
      }
      certificateUrl
      certificateNumber
      completionStatus
    }
  }
`;

// Function to fetch certificate data
const getCertificateData = async (slug: string, email: string) => {
  try {
    const response = await graphQLClient.request(GET_CERTIFICATE_DATA, {
      slug,
      email,
    });
    return response.certificates[0];
  } catch (error) {
    console.error("Error fetching certificate:", error);
    throw error;
  }
};

const CREATE_CERTIFICATE = gql`
  mutation CreateCertificate($slug: String!, $email: String!) {
    createCertificate(
      data: {
        course: { connect: { slug: $slug } }
        user: { connect: { email: $email } }
        issueDate: "${new Date().toISOString()}"
        completionStatus: "completed"
      }
    ) {
      id
      issueDate
      course {
        name
        id
        slug
      }
      user {
        name
        email
      }
      certificateUrl
      certificateNumber
      completionStatus
    }
  }
`;

const fetchOrCreateCertificate = async (slug: string, email: string) => {
  try {
    // First, try to fetch existing certificate
    const existingCertificate = await getCertificateData(slug, email);
    
    if (existingCertificate) {
      return existingCertificate;
    }

    // If no certificate exists, create a new one
    const response = await graphQLClient.request(CREATE_CERTIFICATE, {
      slug,
      email,
    });

    // Publish the certificate
    if (response.createCertificate?.id) {
      await graphQLClient.request(
        gql`
          mutation PublishCertificate($id: ID!) {
            publishCertificate(where: { id: $id }) {
              id
            }
          }
        `,
        { id: response.createCertificate.id }
      );
    }

    return response.createCertificate;
  } catch (error) {
    console.error("Error in fetchOrCreateCertificate:", error);
    throw error;
  }
};

const getUserEnrollments = async (email) => {
  const query = gql`
    query GetUserEnrollments($email: String!) {
      userEnrollments(where: { userInfo: { email: $email } }) {
        id
        courseList {
          id
          name
          slug
        }
      }
    }
  `;

  const variables = { email };

  try {
    const result = await request(MASTER_URL, query, variables);
    return result;
  } catch (error) {
    console.error("Error fetching user enrollments:", error);
    throw new Error("An error occurred while fetching user enrollments. Please try again later.");
  }
};

const CREATE_EXAM_ATTEMPT = gql`
  mutation CreateExamAttempt(
    $examId: ID!
    $userId: ID!
    $attemptsLeft: Int!
    $examIsDisabled: Boolean!
  ) {
    createExamAttempt(
      data: {
        exams: { connect: { id: $examId } }
        userInfos: { connect: { id: $userId } }
        attemptsLeft: $attemptsLeft
        examIsDisabled: $examIsDisabled
      }
    ) {
      id
      attemptsLeft
      examIsDisabled
      exams {
        id
        examName {
          name
        }
      }
      userInfos {
        id
        email
      }
    }
  }
`;

const createExamAttempt = async ({
  exams,
  userInfos,
  attemptsLeft,
  examIsDisabled
}: {
  exams: { connect: { id: string } };
  userInfos: { connect: { id: string } };
  attemptsLeft: number;
  examIsDisabled: boolean;
}) => {
  try {
    const variables = {
      examId: exams.connect.id,
      userId: userInfos.connect.id,
      attemptsLeft,
      examIsDisabled
    };

    const result = await graphQLClient.request(CREATE_EXAM_ATTEMPT, variables);
    
    // Now publish the exam attempt
    if (result.createExamAttempt?.id) {
      const PUBLISH_EXAM_ATTEMPT = gql`
        mutation PublishExamAttempt($id: ID!) {
          publishExamAttempt(where: { id: $id }) {
            id
          }
        }
      `;
      await graphQLClient.request(PUBLISH_EXAM_ATTEMPT, { id: result.createExamAttempt.id });
    }
    
    return result;
  } catch (error) {
    console.error('Error creating exam attempt:', error);
    throw new Error('Failed to create exam attempt');
  }
};

const UPDATE_EXAM_ATTEMPT = gql`
  mutation UpdateExamAttempt(
    $id: ID!
    $attemptsLeft: Int!
    $examIsDisabled: Boolean!
  ) {
    updateExamAttempt(
      where: { id: $id }
      data: {
        attemptsLeft: $attemptsLeft
        examIsDisabled: $examIsDisabled
      }
    ) {
      id
      attemptsLeft
      examIsDisabled
    }
    publishExamAttempt(where: { id: $id }) {
      id
    }
  }
`;

const updateExamAttemptCount = async ({
  id,
  attemptsLeft,
  examIsDisabled
}: {
  id: string;
  attemptsLeft: number;
  examIsDisabled: boolean;
}) => {
  try {
    const variables = {
      id,
      attemptsLeft,
      examIsDisabled
    };

    const result = await graphQLClient.request(UPDATE_EXAM_ATTEMPT, variables);
    return result;
  } catch (error) {
    console.error('Error updating exam attempt:', error);
    throw new Error('Failed to update exam attempt');
  }
};

const GET_EXAM_ATTEMPTS = gql`
  query GetExamAttempts($examId: ID!, $userId: ID!) {
    examAttempts(
      where: { 
        AND: [
          { exams_some: { id: $examId } },
          { userInfos_some: { id: $userId } }
        ]
      }
      orderBy: publishedAt_DESC
      first: 1
    ) {
      id
      attemptsLeft
      examIsDisabled
    }
  }
`;

const getExamAttempts = async (examId: string, userId: string) => {
  try {
    const variables = {
      examId,
      userId
    };

    const result = await graphQLClient.request(GET_EXAM_ATTEMPTS, variables);
    return result.examAttempts[0];
  } catch (error) {
    console.error('Error getting exam attempts:', error);
    throw new Error('Failed to get exam attempts');
  }
};

const UPDATE_ENROLLMENT_STATUS = gql`
  mutation UpdateEnrollmentStatus($email: String!, $courseId: String!, $status: EnrollmentStatus!) {
    # First try to update existing enrollment
    updateManyUserEnrollments(
      where: { 
        userInfo: { email: $email }, 
        coursesList: { slug: $courseId }
      }
      data: { enrolledStatus: $status }
    ) {
      count
    }
    
    # If no enrollment exists, create one
    createUserEnrollment(
      data: {
        enrolledStatus: $status,
        userInfo: { connect: { email: $email } },
        coursesList: { connect: { slug: $courseId } }
      }
    ) {
      id
      enrolledStatus
    }
    
    # Publish both the update and create operations
    publishManyUserEnrollmentsConnection {
      edges {
        node {
          id
        }
      }
    }
    publishUserEnrollment(where: { coursesList: { slug: $courseId }, userInfo: { email: $email } }) {
      id
    }
  }
`;

const updateEnrollmentStatus = async (email: string, courseId: string, status: string) => {
  const query = UPDATE_ENROLLMENT_STATUS;
  const variables = { email, courseId, status };
  
  try {
    const result = await request(MASTER_URL, query, variables);
    return result;
  } catch (error) {
    console.error("Error updating enrollment status:", error);
    throw error;
  }
};

// Mapping of API IDs to display names
export const airportDisplayNames = {
  // International Airports
  capeTownInternationalAirportCpt: "Cape Town International Airport (CPT)",
  durbanInternationalAirportDur: "Durban International Airport (DUR)",
  lanseriaAirportHla: "Lanseria Airport (HLA)",
  oRTamboInternationalAirportJnb: "O.R. Tambo International Airport (JNB)",
  mmabathoInternationalAirportMbd: "Mmabatho International Airport (MBD)",
  krugerMpumalangaInternationalAirportMqp: "Kruger Mpumalanga International Airport (MQP)",
  polokwaneInternationalAirportPtg: "Polokwane International Airport (PTG)",
  pilanesbergInternationalAirportNty: "Pilanesberg International Airport (NTY)",
  
  // Regular Scheduled Flights
  bloemfonteinAirportBfn: "Bloemfontein Airport (BFN)",
  eastLondonAirportEls: "East London Airport (ELS)",
  georgeAirportGrj: "George Airport (GRJ)",
  eastgateAirportHds: "Eastgate Airport (HDS)",
  kimberleyAirportKim: "Kimberley Airport (KIM)",
  malaMalaAirportAam: "Mala Mala Airport (AAM)",
  margateAirportMgh: "Margate Airport (MGH)",
  kDMatanzimaUtt: "K.D. Matanzima Airport (UTT)",
  phalaborwaAirportPhw: "Phalaborwa Airport (PHW)",
  pietermaritzburgAirportPzb: "Pietermaritzburg Airport (PZB)",
  portElizabethAirportPlz: "Port Elizabeth Airport (PLZ)",
  upingtonAirportUtn: "Upington Airport (UTN)",
  richardsBayAirportRcb: "Richards Bay Airport (RCB)",

  // Eastern Cape Province
  aliwalNorthAirportFaan: "Aliwal North Airport (FAAN)",
  bishoAirportBiy: "Bisho Airport (BIY)",
  cradockAirportCdo: "Cradock Airport (CDO)",
  butterworthAirportUte: "Butterworth Airport (UTE)",
  graaffReinetAirportFagr: "Graaff Reinet Airport (FAGR)",
  grahamstownAirportFagr: "Grahamstown Airport (FAGR)",
  lusikisikiAirportLuj: "Lusikisiki Airport (LUJ)",
  mzambaAirportMzf: "Mzamba Airport (MZF)",
  portAlfredAirportAfd: "Port Alfred Airport (AFD)",
  portStJohnsAirportJoh: "Port St Johns Airport (JOH)",
  queenstownAirportUtw: "Queenstown Airport (UTW)",

  // Free State Province
  bethlehemAirportFabm: "Bethlehem Airport (FABM)",
  bothavilleAirportFabo: "Bothaville Airport (FABO)",
  ficksburgAirportFcb: "Ficksburg Airport (FCB)",
  gariepDamAirportFahv: "Gariep Dam Airport (FAHV)",
  harrismithAirportHrs: "Harrismith Airport (HRS)",
  newTempeAirportFatp: "New Tempe Airport (FATP)",
  parysAirportFapy: "Parys Airport (FAPY)",
  thabaNchuAirportTcu: "Thaba Nchu Airport (TCU)",
  welkomAirportWel: "Welkom Airport (WEL)",

  // Gauteng Province
  brakpanAirportFabb: "Brakpan Airport (FABB)",
  grandCentralAirportGcj: "Grand Central Airport (GCJ)",
  randAirportQra: "Rand Airport (QRA)",
  krugersdorpAirportFakr: "Krugersdorp Airport (FAKR)",
  wonderboomAirportPry: "Wonderboom Airport (PRY)",

  // KwaZulu-Natal Province
  virginiaAirportVir: "Virginia Airport (VIR)",
  empangeniAirportEmg: "Empangeni Airport (EMG)",
  greytownAirportFagy: "Greytown Airport (FAGY)",
  hluhluweAirportHlw: "Hluhluwe Airport (HLW)",
  ladysmithAirportLay: "Ladysmith Airport (LAY)",
  mtubtubaAirportDuk: "Mtubtuba Airport (DUK)",
  mkuzeAirportMzq: "Mkuze Airport (MZQ)",
  newcastleAirportNcs: "Newcastle Airport (NCS)",
  ulundiAirportUld: "Ulundi Airport (ULD)",
  vryheidAirportVyd: "Vryheid Airport (VYD)",

  // Limpopo Province
  alldaysAirportAdy: "Alldays Airport (ADY)",
  giyaniAirportGiy: "Giyani Airport (GIY)",
  mitimbaAirportEll: "Mitimba Airport (ELL)",
  modimolleAirportFany: "Modimolle Airport (FANY)",
  musinaAirportMez: "Musina Airport (MEZ)",
  pietersburgMunicipalAirportFapi: "Pietersburg Municipal Airport (FAPI)",
  pRMphephuAirportFapi: "P.R. Mphephu Airport (FAPI)",
  tzaneenAirportLta: "Tzaneen Airport (LTA)",

  // Mpumalanga Province
  ermeloAirportFaeo: "Ermelo Airport (FAEO)",
  hazyviewAirportHzv: "Hazyview Airport (HZV)",
  komatipoortAirportKof: "Komatipoort Airport (KOF)",
  malelaneAirportLle: "Malelane Airport (LLE)",
  marbleHallAirportFami: "Marble Hall Airport (FAMI)",
  middelburgAirportFamb: "Middelburg Airport (FAMB)",
  nelspruitAirportNlp: "Nelspruit Airport (NLP)",
  ngalaAirportNgl: "Ngala Airport (NGL)",
  secundaAirportZec: "Secunda Airport (ZEC)",
  skukuzaAirportSzk: "Skukuza Airport (SZK)",

  // Northern Cape Province
  aggeneysAirportAgz: "Aggeneys Airport (AGZ)",
  alexanderBayAirportAlj: "Alexander Bay Airport (ALJ)",
  alkantpanAirportFaco: "Alkantpan Airport (FACO)",
  calviniaAirportFacv: "Calvinia Airport (FACV)",
  kleinzeeAirportKlz: "Kleinzee Airport (KLZ)",
  koiingnaasAirportKig: "Koiingnaas Airport (KIG)",
  johanPienaarAirportKmh: "Johan Pienaar Airport (KMH)",
  finschMineAirportLmr: "Finsch Mine Airport (LMR)",
  oranjemundAirportOmd: "Oranjemund Airport (OMD)",
  prieskaAirportPrk: "Prieska Airport (PRK)",
  sishenAirportSis: "Sishen Airport (SIS)",
  springbokAirportSbu: "Springbok Airport (SBU)",
  vredendalAirportVre: "Vredendal Airport (VRE)",

  // North West Province
  klerksdorpAirportKxe: "Klerksdorp Airport (KXE)",
  lichtenburgAirportFali: "Lichtenburg Airport (FALI)",
  potchefstroomAirportFaps: "Potchefstroom Airport (FAPS)",
  reiviloAirportRvo: "Reivilo Airport (RVO)",
  rustenburgAirportFarg: "Rustenburg Airport (FARG)",
  vryburgAirportVru: "Vryburg Airport (VRU)",

  // Western Cape Province
  mosselBayAirportMzy: "Mossel Bay Airport (MZY)",
  oudtshoornAirportOuh: "Oudtshoorn Airport (OUH)",
  plettenbergBayAirportPbz: "Plettenberg Bay Airport (PBZ)",
  robertsonAirportRod: "Robertson Airport (ROD)",
  hendrikSwellengrebelAirportFasx: "Hendrik Swellengrebel Airport (FASX)",
  vredenburgAirportSdb: "Vredenburg Airport (SDB)"
};

const getAirportOptions = async () => {
  const query = gql`
    query GetAirportOptions {
      __type(name: "AirportsInSa") {
        enumValues {
          name
        }
      }
    }
  `;

  try {
    const result = await graphQLClient.request(query);
    // Sort airports alphabetically by their display names
    return result.__type.enumValues
      .map(option => ({
        value: option.name,
        label: airportDisplayNames[option.name] || option.name
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
  } catch (error) {
    console.error("Error fetching airport options:", error);
    throw error;
  }
};

const GlobalApi = {
  getAllCourseList,
  getSideBanner,
  getCourseById,
  enrollToCourse,
  checkUserEnrolledToCourse,
  getUserEnrolledCourseDetails,
  markChapterCompleted,
  getUserAllEnrolledCourseList,
  addNewMember,
  checkForMembership,
  createUserProfile,
  updateUserProfile,
  publishUserInfo,
  getUserProfile,
  getAllExamList,
  getTestimonials,
  getCertificateData,
  fetchOrCreateCertificate,
  getUserEnrollments,
  createExamAttempt,
  updateExamAttemptCount,
  getExamAttempts,
  updateEnrollmentStatus,
  getAirportOptions
};

export default GlobalApi;
export const dynamic = "force-dynamic"; // defaults to auto

export async function GET(request: Request) {
  // Basic logic for handling GET request
  return new Response(JSON.stringify({ message: 'GET request received' }), {
	headers: { 'Content-Type': 'application/json' },
  });
}

export async function POST(request: Request) {
  // Basic logic for handling POST request
  const data = await request.json();
  return new Response(JSON.stringify({ message: 'POST request received', data }), {
	headers: { 'Content-Type': 'application/json' },
  });
}

export async function PUT(request: Request) {
  // Basic logic for handling PUT request
  const data = await request.json();
  return new Response(JSON.stringify({ message: 'PUT request received', data }), {
	headers: { 'Content-Type': 'application/json' },
  });
}

export async function DELETE(request: Request) {
  // Basic logic for handling DELETE request
  return new Response(JSON.stringify({ message: 'DELETE request received' }), {
	headers: { 'Content-Type': 'application/json' },
  });
}

"use client";

import React, { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Select from 'react-select';
import {
  createUserProfile,
  getUserProfile,
  updateUserProfile,
} from "@/app/_utils/GlobalApi";
import GlobalApi from "@/app/_utils/GlobalApi";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

function MyProfile({ isOnboarding = false }: { isOnboarding?: boolean }) {
  const { data: session } = useSession();
  const router = useRouter();
  const { user } = useUser();

  const [formData, setFormData] = useState({
    id: "",
    name: "",
    surname: "",
    email: "",
    cellphoneNumber: "",
    idNumber: "",
    airport: "",
    physicalAddress: "",
    companyName: "",
  });

  const [isExistingUser, setIsExistingUser] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [airportOptions, setAirportOptions] = useState([]);

  useEffect(() => {
    const fetchAirportOptions = async () => {
      try {
        const options = await GlobalApi.getAirportOptions();
        setAirportOptions(options);
      } catch (error) {
        console.error("Error fetching airport options:", error);
      }
    };
    fetchAirportOptions();
  }, []);

  useEffect(() => {
    if (user) {
      const fetchUserProfile = async () => {
        try {
          const profile = await getUserProfile(
            user.emailAddresses[0]?.emailAddress,
          );
          if (profile) {
            setFormData({
              id: profile.id,
              name: profile.name || "",
              surname: profile.surname || "",
              email: profile.email || "",
              cellphoneNumber: profile.cellphoneNumber || "",
              idNumber: profile.idNumber || "",
              airport: profile.airport || "",
              physicalAddress: profile.physicalAddress || "",
              companyName: profile.companyName || "",
            });
            setIsExistingUser(true);
          } else {
            const primaryEmail = user.emailAddresses[0]?.emailAddress || "";
            const phoneNumber = user.phoneNumbers?.[0]?.phoneNumber || "";
            const address = typeof user.unsafeMetadata?.address === 'string' ? user.unsafeMetadata.address : "";
            
            setFormData((prevData) => ({
              ...prevData,
              name: user.firstName || "",
              surname: user.lastName || "",
              email: primaryEmail,
              cellphoneNumber: phoneNumber.replace(/\D/g, ""),
              idNumber: "",
              airport: "",
              physicalAddress: address,
              companyName: "",
            }));
            setIsExistingUser(false);
          }
        } catch (error) {
          console.error("Error fetching user profile:", error.message);
          const primaryEmail = user.emailAddresses[0]?.emailAddress || "";
          const phoneNumber = user.phoneNumbers?.[0]?.phoneNumber || "";
          const address = typeof user.unsafeMetadata?.address === 'string' ? user.unsafeMetadata.address : "";
          
          setFormData((prevData) => ({
            ...prevData,
            name: user.firstName || "",
            surname: user.lastName || "",
            email: primaryEmail,
            cellphoneNumber: phoneNumber.replace(/\D/g, ""),
            idNumber: "",
            airport: "",
            physicalAddress: address,
            companyName: "",
          }));
          setIsExistingUser(false);
        }
      };
      fetchUserProfile();
    }
  }, [user]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleAirportChange = (selectedOption) => {
    setFormData((prevData) => ({
      ...prevData,
      airport: selectedOption?.value || "",
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    const {
      id,
      name,
      surname,
      email,
      cellphoneNumber,
      idNumber,
      airport,
      physicalAddress,
      companyName,
    } = formData;

    try {
      if (isExistingUser) {
        await updateUserProfile({
          id,
          name,
          surname,
          email,
          cellphoneNumber: Number(cellphoneNumber),
          idNumber: Number(idNumber),
          airport,
          physicalAddress,
          companyName,
        });
        toast.success("User profile updated successfully");
        setTimeout(() => {
          router.push("/dashboard");
        }, 1000);
      } else {
        await createUserProfile({
          name,
          surname,
          email,
          cellphoneNumber: Number(cellphoneNumber),
          idNumber: Number(idNumber),
          airport,
          physicalAddress,
          companyName,
        });
        toast.success("User profile created successfully");
        setTimeout(() => {
          router.push("/dashboard");
        }, 1000);
      }
    } catch (error) {
      toast.error("Error creating or updating user profile");
      console.error("Error creating or updating user profile:", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  const handleCancel = () => {
    if (typeof window !== "undefined") {
      router.push("/dashboard");
    }
  };

  return (
    <div className="mb-5shadow-md rounded-lg border border-gray-200 bg-white p-6">
      <ToastContainer />
      <div className="mb-6 flex items-center">
        <h1 className="text-[20px] font-bold text-primary">Profile</h1>
      </div>
      <form onSubmit={handleSubmit} className="mb-4">
        <div className="space-y-4">
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700"
            >
              Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 text-black shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              disabled={isLoading}
            />
          </div>
          <div>
            <label
              htmlFor="surname"
              className="block text-sm font-medium text-gray-700"
            >
              Surname
            </label>
            <input
              type="text"
              id="surname"
              name="surname"
              value={formData.surname}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 text-black shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              disabled={isLoading}
            />
          </div>
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700"
            >
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 text-black shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              disabled={isLoading}
            />
          </div>
          <div>
            <label
              htmlFor="cellphone"
              className="block text-sm font-medium text-gray-700"
            >
              Cellphone
            </label>
            <input
              type="text"
              id="cellphone"
              name="cellphoneNumber"
              value={formData.cellphoneNumber}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 text-black shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              placeholder="Enter your cellphone number"
              disabled={isLoading}
            />
          </div>
          <div>
            <label
              htmlFor="idNumber"
              className="block text-sm font-medium text-gray-700"
            >
              ID Number
            </label>
            <input
              type="text"
              id="idNumber"
              name="idNumber"
              value={formData.idNumber}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 text-black shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              placeholder="Enter your ID number"
              disabled={isLoading}
            />
          </div>
          <div>
            <label
              htmlFor="airport"
              className="block text-sm font-medium text-gray-700"
            >
              Airport
            </label>
            <Select
              id="airport"
              name="airport"
              value={airportOptions.find(option => option.value === formData.airport)}
              onChange={handleAirportChange}
              options={airportOptions}
              isDisabled={isLoading}
              isClearable
              placeholder="Search for an airport..."
              className="mt-1"
              classNamePrefix="react-select"
              styles={{
                control: (base) => ({
                  ...base,
                  backgroundColor: 'white',
                }),
                menu: (base) => ({
                  ...base,
                  backgroundColor: 'white',
                  zIndex: 9999
                }),
                option: (base, state) => ({
                  ...base,
                  backgroundColor: state.isFocused ? '#EEF2FF' : 'white',
                  color: 'black',
                  ':hover': {
                    backgroundColor: '#E0E7FF'
                  }
                })
              }}
              theme={(theme) => ({
                ...theme,
                colors: {
                  ...theme.colors,
                  primary: '#4F46E5',
                  primary25: '#EEF2FF',
                  primary50: '#E0E7FF',
                }
              })}
            />
          </div>
          <div>
            <label
              htmlFor="companyName"
              className="block text-sm font-medium text-gray-700"
            >
              Company Name (Optional)
            </label>
            <input
              type="text"
              id="companyName"
              name="companyName"
              value={formData.companyName}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 text-black shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              placeholder="Enter your company name (optional)"
              disabled={isLoading}
            />
          </div>
          <div>
            <label
              htmlFor="physicalAddress"
              className="block text-sm font-medium text-gray-700"
            >
              Physical Address
            </label>
            <textarea
              id="physicalAddress"
              name="physicalAddress"
              value={formData.physicalAddress}
              onChange={handleInputChange}
              rows={2}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 text-black shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              placeholder="Enter your physical address"
              disabled={isLoading}
            />
          </div>
        </div>
        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={handleCancel}
            className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            disabled={!isExistingUser || isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={`rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
              isLoading ? "cursor-not-allowed opacity-50" : "hover:bg-primary"
            }`}
            disabled={isLoading}
          >
            {isLoading ? (
              <svg
                className="h-5 w-5 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8v8H4z"
                ></path>
              </svg>
            ) : isExistingUser ? (
              "Update"
            ) : (
              "Save"
            )}
          </button>
        </div>
      </form>
    </div>
  );
}

export default MyProfile;

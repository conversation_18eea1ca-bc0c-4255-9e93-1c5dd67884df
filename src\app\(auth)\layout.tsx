"use client";
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';

import { Inter } from "next/font/google";
import "node_modules/react-modal-video/css/modal-video.css";
import "../../styles/index.css";
import { Providers } from "../providers";


export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
    <html lang="en">
        <body><Providers>{children}</Providers></body>
    </html>
    </ClerkProvider>
      
  );
}

import React, { useState } from 'react';

interface ModuleCompletionProps {
  item: any;
  isModuleComplete: boolean;
  onComplete: () => void;
}

const ModuleCompletion: React.FC<ModuleCompletionProps> = ({ item, isModuleComplete, onComplete }) => {
  const handleCompleteChange = () => {
    if (!isModuleComplete) {
      onComplete();
    }
  };

  return (
    <div
      className={`m-2 flex cursor-pointer items-center justify-between rounded-sm border p-2 px-4 text-[14px] 
      ${isModuleComplete ? 'bg-green-200 text-green-700' : 'hover:bg-gray-200 hover:text-gray-500'}`}
      onClick={handleCompleteChange}
    >
      {item.index + 1}. {item.name}
      <input
        type="radio"
        checked={isModuleComplete}
        onChange={handleCompleteChange}
      />
    </div>
  );
};

export default ModuleCompletion;

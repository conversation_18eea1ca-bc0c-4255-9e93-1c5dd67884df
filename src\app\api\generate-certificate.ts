import { NextApiRequest, NextApiResponse } from 'next';
import ReactPDF from '@react-pdf/renderer';
import Certificate from '../../components/Certificate/Certificate'; // Adjust the path as needed
import { ReactElement } from 'react';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const { studentName, courseName, completionDate, examScore } = req.body;

    if (examScore < 80) {
      return res.status(400).json({ message: 'Score is less than 80' });
    }

    try {
      const pdfStream = await ReactPDF.renderToStream(
        Certificate({
          studentName,
          courseName,
          completionDate,
          examScore,
        }) as ReactElement
      );

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename=certificate.pdf`);
      pdfStream.pipe(res);
      pdfStream.on('end', () => res.end());
    } catch (error) {
      console.error('Error generating PDF:', error);
      res.status(500).json({ message: 'Error generating PDF' });
    }
  } else {
    res.status(405).json({ message: 'Method not allowed' });
  }
}

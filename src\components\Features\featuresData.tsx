import { Feature } from "@/types/feature";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faShieldAlt, faPlane, faLock, faChartLine, faUserShield, faGlobe } from '@fortawesome/free-solid-svg-icons';


const featuresData: Feature[] = [
  {
    id: 1,
    icon: faShieldAlt,
    title: "Advanced Threat Detection",
    paragraph:
      "Utilize state-of-the-art technology to identify and mitigate potential security threats.",
  },
  {
    id: 2,
    icon: faPlane,
    title: "Comprehensive Security Training",
    paragraph:
      "Provide thorough training programs to ensure all personnel are well-versed in security protocols.",
  },
  {
    id: 3,
    icon:faLock,
    title: "Cybersecurity Integration",
    paragraph:
      "Protect aviation systems from cyber threats with our advanced cybersecurity measures.",
  },
  {
    id: 4,
    icon: faChartLine,
    title: "Real-Time Monitoring",
    paragraph: "Implement real-time monitoring solutions to enhance security and response times.",
  },
  {
    id: 5,
    icon: faUserShield,
    title: "Personalized Security Solutions",
    paragraph: "Offer tailored security solutions to meet the unique needs of each aviation organization.",
  },
  {
    id: 6,
    icon: faGlobe,
    title: "Global Standards Compliance",
    paragraph: "Ensure compliance with international aviation security standards and regulations.",
  },



];
export default featuresData;

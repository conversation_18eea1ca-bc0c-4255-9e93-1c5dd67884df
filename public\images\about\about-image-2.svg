<svg width="500" height="480" viewBox="0 0 500 480" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="500" height="480" rx="2" fill="white"/>
<rect x="35" y="117" width="225" height="156" fill="url(#paint0_linear_1028_964)"/>
<line x1="35" y1="80.5" x2="465" y2="80.5" stroke="#DCE1F1"/>
<rect x="290" y="117" width="175" height="156" rx="2" fill="#EAEFF7"/>
<rect x="35" y="303" width="430" height="142" rx="2" fill="#E9EEF6"/>
<rect x="194" y="353" width="229" height="8" rx="2" fill="white"/>
<rect x="194" y="370" width="152" height="8" rx="2" fill="white"/>
<rect x="194" y="387" width="187" height="8" rx="2" fill="white"/>
<rect x="59" y="324" width="100" height="100" rx="2" fill="white"/>
<rect x="138" y="35" width="327" height="18" rx="2" fill="#E7ECF5"/>
<circle cx="39.2391" cy="44.2391" r="4.23913" fill="#FA584E"/>
<circle cx="54.5" cy="44.2391" r="4.23913" fill="#FBB527"/>
<circle cx="69.7609" cy="44.2391" r="4.23913" fill="#32C133"/>
<circle opacity="0.8" cx="377.5" cy="194.5" r="37.5" fill="#4A6CF7"/>
<mask id="mask0_1028_964" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="340" y="157" width="75" height="75">
<circle opacity="0.8" cx="377.5" cy="194.5" r="37.5" fill="#4A6CF7"/>
</mask>
<g mask="url(#mask0_1028_964)">
<circle opacity="0.8" cx="377.5" cy="194.5" r="37.5" fill="url(#paint1_radial_1028_964)"/>
<g opacity="0.8" filter="url(#filter0_f_1028_964)">
<circle cx="380.809" cy="176.853" r="15.4412" fill="white"/>
</g>
</g>
<path d="M342.034 231.847C324.964 262.749 277.939 313.186 226.394 267.718" stroke="url(#paint2_linear_1028_964)" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="2 2"/>
<path d="M339.077 230.867L343.973 228.971L344.562 233.656" stroke="url(#paint3_linear_1028_964)" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="2 2"/>
<defs>
<filter id="filter0_f_1028_964" x="344.368" y="140.412" width="72.8823" height="72.8828" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.5" result="effect1_foregroundBlur_1028_964"/>
</filter>
<linearGradient id="paint0_linear_1028_964" x1="434.5" y1="210" x2="48.2226" y2="58.8072" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="#F0F2F9"/>
</linearGradient>
<radialGradient id="paint1_radial_1028_964" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(377.5 194.5) rotate(90) scale(40.2574)">
<stop stop-opacity="0.47"/>
<stop offset="1" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint2_linear_1028_964" x1="358.554" y1="226.723" x2="242.956" y2="282.349" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.48"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_1028_964" x1="343.992" y1="228.548" x2="340.23" y2="234.571" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.48"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>

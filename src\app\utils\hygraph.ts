// import fetch from 'node-fetch';
import fetch from 'isomorphic-fetch';

interface ClerkUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

export async function createUserInHygraph(user: ClerkUser) {
  const mutation = `
    mutation {
      createUser(data: {
        name: "${user.first_name} ${user.last_name}",
        email: "${user.email}",
        clerkId: "${user.id}"
      }) {
        id
      }
    }
  `;
  
  const response = await fetch(process.env.HYGRAPH_API_ENDPOINT!, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.HYGRAPH_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query: mutation })
  });
  const result = await response.json();
  return result;
}

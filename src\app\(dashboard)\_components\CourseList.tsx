import React, { useEffect, useState, useCallback } from "react";
import CourseItem from "./CourseItem";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import GlobalApi from "@/app/_utils/GlobalApi";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import CourseModal from "./CourseModal"; // Import the CourseModal component
import { useUser } from "@clerk/nextjs";

function CourseList() {
  const { user } = useUser();
  const userName = user ? `${user.firstName} ${user.lastName}` : "Guest";

  const [courseList, setCourseList] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [enrolledCourses, setEnrolledCourses] = useState([]);

  const getUserEnrolledCourses = useCallback(() => {
    GlobalApi.getUserEnrollments(user?.primaryEmailAddress?.emailAddress).then((resp) => {
      const enrollments = resp?.userEnrollments || [];
      setEnrolledCourses(enrollments);
    }).catch(error => {
      console.error('Error fetching enrolled courses:', error);
    });
  }, [user?.primaryEmailAddress?.emailAddress]);

  useEffect(() => {
    getAllCourses();
    if (user) {
      getUserEnrolledCourses();
    }
  }, [user, getUserEnrolledCourses]);

  const getAllCourses = () => {
    GlobalApi.getAllCourseList().then((resp) => {
      setCourseList(resp?.coursesLists || []);
    });
  };

  const handleOpenModal = (course) => {
    setSelectedCourse(course);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setSelectedCourse(null);
    setIsModalOpen(false);
  };

  const handleEnroll = (courseId) => {
    const newEnrollment = { courseList: { id: courseId } };
    setEnrolledCourses(prevEnrolled => [...prevEnrolled, newEnrollment]);
    handleCloseModal();
  };

  const isEnrolled = (courseId) => {
    return enrolledCourses.some(enrollment => enrollment.courseList?.id === courseId);
  };

  return (
    <div className="mt-3 rounded-lg bg-white p-5">
      {/* Title and Filter */}
      <div className="flex items-center justify-between">
        <h2 className="text-[20px] font-bold text-primary">All Courses</h2>
        <Select>
          <SelectTrigger className="w-[180px] text-black">
            <SelectValue placeholder="Filter" />
          </SelectTrigger>
          <SelectContent className="w-[180px] bg-white text-black">
            <SelectItem className="w-[180px] bg-white text-black" value="light">
              All
            </SelectItem>
            <SelectItem value="dark">Paid</SelectItem>
            <SelectItem value="system">Free</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Course List Rendering */}
      <div className="mt-4 grid grid-cols-2 gap-4 lg:grid-cols-3">
        {courseList.map((item) => (
          <div
            key={item.id}
            className="group relative mb-4 w-[250px] overflow-hidden rounded-xl bg-white text-gray-800 shadow-one duration-300 hover:shadow-two"
          >
            <CourseItem course={item} />
            <div>
              {isEnrolled(item.id) ? (
                <Link href={`/course-preview/${item.slug}`}>
                  <Button className="bottom-4 left-4 w-full rounded bg-blue-500 px-4 py-2 font-bold text-white hover:bg-blue-600">
                    Continue Watching
                  </Button>
                </Link>
              ) : (
                <Button 
                  className="bottom-4 left-4 w-full rounded bg-green-500 px-4 py-2 font-bold text-white hover:bg-green-600"
                  onClick={() => handleOpenModal(item)}
                >
                  Enroll Now
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Course Modal */}
      {isModalOpen && (
        <CourseModal
          course={selectedCourse}
          onClose={handleCloseModal}
          onEnroll={handleEnroll}
          userName={userName}
        />
      )}
    </div>
  );
}

export default CourseList;
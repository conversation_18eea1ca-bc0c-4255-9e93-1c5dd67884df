import React, { useEffect, useState } from 'react';

interface TimerProps {
  duration: number; // in minutes
  onTimeUp: () => void;
  isActive: boolean;
}

const Timer: React.FC<TimerProps> = ({ duration, onTimeUp, isActive }) => {
  const [timeLeft, setTimeLeft] = useState(duration * 60); // Convert minutes to seconds
  const [isWarning, setIsWarning] = useState(false);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((prevTime) => {
          const newTime = prevTime - 1;
          // Set warning when 5 minutes remaining
          if (newTime === 300) {
            setIsWarning(true);
          }
          return newTime;
        });
      }, 1000);
    } else if (timeLeft === 0) {
      onTimeUp();
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [timeLeft, isActive, onTimeUp]);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    const format = (num: number): string => num.toString().padStart(2, '0');

    return hours > 0
      ? `${format(hours)}:${format(minutes)}:${format(remainingSeconds)}`
      : `${format(minutes)}:${format(remainingSeconds)}`;
  };

  return (
    <div className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg ${
      isWarning ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
    }`}>
      <div className="text-sm font-semibold mb-1">Time Remaining</div>
      <div className={`text-2xl font-bold ${isWarning ? 'animate-pulse' : ''}`}>
        {formatTime(timeLeft)}
      </div>
      {isWarning && (
        <div className="text-xs mt-1 text-red-600">
          5 minutes remaining!
        </div>
      )}
    </div>
  );
};

export default Timer;

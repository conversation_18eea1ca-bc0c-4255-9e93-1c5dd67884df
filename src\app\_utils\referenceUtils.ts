// src/utils/referenceUtils.ts
// @ts-ignore
import { GraphQLClient, gql } from 'graphql-request';

const hygraphClient = new GraphQLClient(process.env.NEXT_PUBLIC_MASTER_URL, {
  headers: {
    authorization: `Bearer ${process.env.NEXT_PUBLIC_HYGRAPH_API_KEY}`,
  },
});

export const generateReferenceCode = async (courseId: string, expirationDays: number = 30) => {
  const code = `REF-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
  
  // Set expiration date with time set to end of day in UTC
  const expirationDate = new Date();
  expirationDate.setDate(expirationDate.getDate() + expirationDays);
  expirationDate.setUTCHours(23, 59, 59, 999);

  const mutation = gql`
    mutation CreateReferenceCode($code: String!, $courseId: String!, $expirationDate: DateTime!) {
      createReferenceCode(
        data: { 
          code: $code, 
          courseId: $courseId, 
          expirationDate: $expirationDate, 
          isUsed: false,
          course: { connect: { id: $courseId } }
        }
      ) {
        id
        code
        expirationDate
        course {
          id
          name
          slug
        }
      }
      publishReferenceCode(where: { code: $code }) {
        id
      }
    }
  `;

  try {
    await hygraphClient.request(mutation, { 
      code, 
      courseId, 
      expirationDate: expirationDate.toISOString() 
    });
    return code;
  } catch (error) {
    console.error('Error generating reference code:', error);
    throw error;
  }
};
"use client";

import React, { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import CertificateGeneratorHTML from '@/components/Certificate/CertificateGeneratorHTML';

const CertificateTestPage = () => {
  const { user, isLoaded } = useUser();
  const [examScore, setExamScore] = useState<number>(85);
  const [message, setMessage] = useState<string>('');

  const handleSuccess = (certificateUrl: string) => {
    setMessage('Certificate generated successfully!');
    setTimeout(() => setMessage(''), 3000);
  };

  const handleError = (error: string) => {
    setMessage(`Error: ${error}`);
    setTimeout(() => setMessage(''), 5000);
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-red-500">Please sign in to access this page</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Certificate Generator Test
          </h1>
          <p className="text-lg text-gray-600">
            Test the HTML-based certificate generation system
          </p>
        </div>

        {message && (
          <div className={`mb-6 p-4 rounded-md ${
            message.startsWith('Error') 
              ? 'bg-red-50 border border-red-200 text-red-800' 
              : 'bg-green-50 border border-green-200 text-green-800'
          }`}>
            {message}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Certificate Generator */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Generate Certificate</h2>
            
            <div className="mb-4">
              <label htmlFor="examScore" className="block text-sm font-medium text-gray-700 mb-2">
                Exam Score (%)
              </label>
              <input
                type="number"
                id="examScore"
                min="0"
                max="100"
                value={examScore}
                onChange={(e) => setExamScore(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">
                Minimum score of 80% required for certificate generation
              </p>
            </div>

            <CertificateGeneratorHTML
              courseName="Aviation Security (AVSEC) General Awareness"
              examScore={examScore}
              onSuccess={handleSuccess}
              onError={handleError}
            />
          </div>

          {/* Information Panel */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Certificate Information</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900">Student Details</h3>
                <div className="mt-2 text-sm text-gray-600">
                  <p><strong>Name Format:</strong> {user.firstName?.charAt(0).toUpperCase()} {user.lastName}</p>
                  <p><strong>Email:</strong> {user.primaryEmailAddress?.emailAddress}</p>
                </div>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">Certificate Details</h3>
                <div className="mt-2 text-sm text-gray-600">
                  <p><strong>Course:</strong> Aviation Security (AVSEC) General Awareness</p>
                  <p><strong>Current Date:</strong> {new Date().toLocaleDateString('en-GB')}</p>
                  <p><strong>Valid Until:</strong> {new Date(Date.now() + 2 * 365 * 24 * 60 * 60 * 1000).toLocaleDateString('en-GB')}</p>
                </div>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">Placeholders Used</h3>
                <div className="mt-2 text-sm text-gray-600">
                  <ul className="list-disc list-inside space-y-1">
                    <li><code>{'{{NAME}}'}</code> - Student name (J Doe format)</li>
                    <li><code>{'{{ID_NUMBER}}'}</code> - Student ID number</li>
                    <li><code>{'{{CURRENT_DATE}}'}</code> - Current date</li>
                    <li><code>{'{{ISSUED_DATE}}'}</code> - Certificate issue date</li>
                    <li><code>{'{{DATE_PLUS_TWO_YEARS}}'}</code> - Expiry date</li>
                  </ul>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <h4 className="font-medium text-blue-900 mb-2">How it works:</h4>
                <ol className="text-sm text-blue-800 list-decimal list-inside space-y-1">
                  <li>Reads the HTML template from public folder</li>
                  <li>Replaces placeholders with user data</li>
                  <li>Uses Puppeteer to generate PDF</li>
                  <li>Returns PDF for download or preview</li>
                </ol>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">API Endpoints</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-900">POST /api/generate-certificate-html</h3>
              <p className="text-sm text-gray-600 mt-1">
                Generates a certificate PDF using the HTML template with dynamic placeholders.
              </p>
              <div className="mt-2 bg-gray-50 rounded p-3">
                <pre className="text-xs text-gray-700">
{`{
  "courseName": "Aviation Security (AVSEC) General Awareness",
  "examScore": 85
}`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CertificateTestPage;

import React from "react";
import { Book<PERSON><PERSON>, Users, LogOut, Book, User } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

function SideBar() {
  const manu = [
    {
      id: 2,
      name: "My courses",
      icon: Book,
      path: "/dashboard",
    },
    {
      id: 8,
      name: "My Profile",
      icon: User,
      path: "/profile",
    },
  ];

  return (
    <div className="h-screen bg-primary p-5 shadow-sm">
      <Image
        src="images/logo/logo-white1.svg"
        alt="logo"
        width={170}
        height={80}
      />
      <hr className="mt-8" />
      <div className="mt-2">
        {manu.map((item) => (
          <Link key={item.id} href={item.path} passHref>
            <div
              key={item.id} // Added key prop here
              className="group mt-1 flex cursor-pointer items-center gap-3 rounded-md p-3 text-[18px] transition-all duration-200 ease-in-out hover:bg-yellow"
            >
              <item.icon className="text-[18px] group-hover:animate-bounce" />
              <h2>{item.name}</h2>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}

export default SideBar;

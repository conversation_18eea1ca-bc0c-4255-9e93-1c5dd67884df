import React, { useState } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axios from "axios";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useUser } from '@clerk/nextjs';

function CourseEnrollmentForm({ courseSlug, onEnroll, onCodeValid }) {
  const [referenceCode, setReferenceCode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isAlertOpen, setIsAlertOpen] = useState(false);
  const [isSendingTestEmail, setIsSendingTestEmail] = useState(false);
  const { user } = useUser();

  const handleCodeSubmit = async () => {
    if (!referenceCode.trim()) {
      setErrorMessage("Reference code cannot be empty.");
      return;
    }

    setIsSubmitting(true);
    setErrorMessage("");

    try {
      const response = await axios.post("/api/check-reference", {
        referenceCode,
        courseSlug,
      });

      const { isValid, courseInfo } = response.data;

      if (isValid) {
        // Create a new UserEnrollment
        const enrollmentResponse = await axios.post("/api/enroll-course", {
          courseId: courseInfo.id,
          userEmail: user?.primaryEmailAddress?.emailAddress,
        });

        if (enrollmentResponse.data.success) {
          setIsAlertOpen(true);
          onCodeValid(true);
          onEnroll(courseInfo.id);

          // Call the email API to send confirmation
          await axios.post('/api/send-enrollment-email', {
            email: user?.primaryEmailAddress?.emailAddress,
            courseName: courseInfo.name,
          });

          toast.success("Enrollment successful! Check your email for confirmation.");

          // Call handleSendTestEmail for test email
          await handleSendTestEmail();
        } else {
          toast.error("Failed to enroll in the course. Please try again.");
        }
      } else {
        toast.error(response.data.message || "Invalid reference code. Please try again.");
        onCodeValid(false);
      }
    } catch (error) {
      console.error('An error occurred while enrolling in the course');
      toast.error(error.response?.data?.message || "An error occurred while enrolling in the course.");
      if (error.response?.data?.error) {
        console.error('Detailed error:', error.response.data.error);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSendTestEmail = async () => {
    setIsSendingTestEmail(true);
    try {
      await axios.post('/api/send-enrollment-email', {
        email: "<EMAIL>",  // Dummy email for testing
        courseName: "Sample Dummy Course",  // Dummy course name
      });
      toast.success("Test email sent successfully!");
    } catch (error) {
      toast.error("Failed to send test email.");
      console.error(error);
    } finally {
      setIsSendingTestEmail(false);
    }
  };
  
  return (
    <div className="mt-4">
      <input
        type="text"
        value={referenceCode}
        onChange={(e) => setReferenceCode(e.target.value)}
        placeholder="Enter your reference code"
        className={`mb-2 w-full bg-white text-gray-800 rounded-lg border p-2 ${
          errorMessage ? "border-red-500" : ""
        }`}
        disabled={isSubmitting}
      />
      {errorMessage && (
        <p className="mb-2 text-sm text-red-500">{errorMessage}</p>
      )}
      <Button onClick={handleCodeSubmit} disabled={isSubmitting}>
        {isSubmitting ? "Validating..." : "Enroll Now"}
      </Button>

   
      
    </div>
  );
}

export default CourseEnrollmentForm;

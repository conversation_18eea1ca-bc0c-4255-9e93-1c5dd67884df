"use client";

import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { Providers } from "../providers";
import MianNav from "./_components/MianNav";
import SideBar from "./_components/SideBar";
import "../../styles/index.css";
import ProfileCheck from "@/app/(dashboard)/_components/ProfileCheck";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className="bg-gray-100">
          <Providers>
            <ProfileCheck>
              <div>
                <div className="fixed sm:block sm:w-64">
                  <SideBar />
                </div>
                <div className="ml-64">
                  <MianNav />
                  {children}
                </div>
              </div>
            </ProfileCheck>
          </Providers>
        </body>
      </html>
    </ClerkProvider>
  );
}

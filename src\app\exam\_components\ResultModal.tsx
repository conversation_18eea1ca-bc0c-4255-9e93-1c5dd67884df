import React, { useState } from "react";
import Confetti from "react-confetti";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";

interface ResultModalProps {
  open: boolean;
  onClose: () => void;
  passed: boolean;
  resultPercentage: number;
  userName: string;
  examName: string;
  examScore: number;
  attemptsLeft: number;
  onTryAgain: () => void;
}

const ResultModal: React.FC<ResultModalProps> = ({
  open,
  onClose,
  passed,
  resultPercentage,
  userName,
  examName,
  examScore,
  attemptsLeft,
  onTryAgain,
}) => {
  const router = useRouter();
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [isGeneratingCertificate, setIsGeneratingCertificate] = useState(false);

  // Function to send certificate request to admin (existing functionality)
  const handleCertificateRequest = async () => {
    try {
      const response = await fetch("/api/request-certificate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          studentName: userName,
          courseName: examName,
        }),
      });

      if (response.ok) {
        setShowSuccessDialog(true);
      } else {
        const errorText = await response.text();
        throw new Error(`Failed to send certificate request: ${errorText}`);
      }
    } catch (error) {
      console.error("Error sending certificate request:", error);
      toast.error(
        `Failed to request certificate. Please try again. Error: ${error.message}`,
      );
    }
  };

  // Function to generate certificate immediately using HTML template
  const handleGenerateCertificate = async () => {
    try {
      setIsGeneratingCertificate(true);

      // Generate certificate using the new HTML-based approach
      const response = await fetch("/api/generate-certificate-html", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          courseName: "Aviation Security (AVSEC) General Awareness",
          examScore: examScore,
        }),
      });

      if (response.ok) {
        // Get the PDF as a blob
        const blob = await response.blob();

        // Create a URL for the blob
        const url = window.URL.createObjectURL(blob);

        // Open the PDF in a new tab
        window.open(url, "_blank");

        // Also send the certificate request to admin for record keeping
        await handleCertificateRequest();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate certificate");
      }
    } catch (error) {
      console.error("Error generating certificate:", error);
      toast.error(
        `Failed to generate certificate. Please try again. Error: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    } finally {
      setIsGeneratingCertificate(false);
    }
  };

  const handleSuccessDialogClose = () => {
    setShowSuccessDialog(false);
    router.push("/dashboard");
  };

  const handleClose = () => {
    onClose();
    router.push("/dashboard");
  };

  if (!open) return null;

  return (
    <>
      {passed && <Confetti />}
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        {showSuccessDialog ? (
          <div className="mx-auto max-w-md rounded-lg bg-white p-6 shadow-lg">
            <h2 className="mb-4 text-center text-2xl font-bold text-green-600">
              Certificate Request Sent!
            </h2>
            <p className="mb-6 text-center">
              Your request has been sent to the admin. You will be notified when
              the certificate is ready.
            </p>
            <div className="text-center">
              <button
                onClick={handleSuccessDialogClose}
                className="rounded bg-green-500 px-6 py-2 text-white hover:bg-green-600"
              >
                OK
              </button>
            </div>
          </div>
        ) : (
          <div
            className={`mx-auto max-w-md rounded-lg bg-white p-6 shadow-lg ${passed ? "text-green-800" : "text-red-800"}`}
          >
            <h2 className="mb-4 text-center text-2xl font-bold">
              {examName} - Result
            </h2>
            <p className="mb-2 text-center text-lg">Student Name: {userName}</p>
            <p className="mb-2 text-center text-lg">
              Passing Mark: {examScore}%
            </p>
            <p className="mb-4 text-center text-lg">
              Your Mark: <span className="font-bold">{resultPercentage}%</span>
            </p>
            {passed ? (
              <>
                <p className="mb-4 text-center text-xl font-bold text-green-600">
                  Congratulations, you passed!
                </p>
                <Confetti />
                <div className="space-y-3 text-center">
                  <button
                    onClick={handleGenerateCertificate}
                    className="mt-4 w-full rounded bg-green-500 px-4 py-2 text-white hover:bg-green-600"
                    disabled={isGeneratingCertificate}
                  >
                    {isGeneratingCertificate
                      ? "Generating Certificate..."
                      : "Generate Certificate"}
                  </button>
                  <p className="text-sm text-gray-600">
                    Click the button above to generate and download your
                    certificate immediately.
                  </p>
                </div>
              </>
            ) : (
              <>
                <p className="mb-4 text-center text-xl font-bold text-red-600">
                  You failed. Attempts left: {attemptsLeft}
                </p>
                {attemptsLeft > 0 ? (
                  <div className="text-center">
                    <button
                      onClick={onTryAgain}
                      className="mt-4 rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
                    >
                      Try Again
                    </button>
                  </div>
                ) : (
                  <p className="text-center">
                    No attempts left. Please contact support.
                  </p>
                )}
              </>
            )}
            <div className="mt-4 text-center">
              <button
                onClick={handleClose}
                className="rounded bg-gray-300 px-4 py-2 text-gray-800 hover:bg-gray-400"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ResultModal;

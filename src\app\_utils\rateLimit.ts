const WINDOW_SIZE = 60 * 1000; // 1 minute
const MAX_REQUESTS = 5;

const store = new Map();

export function rateLimit(ip: string): boolean {
  const now = Date.now();
  const windowStart = now - WINDOW_SIZE;

  const windowRequests = store.get(ip) || [];
  const requestsInWindow = windowRequests.filter(timestamp => timestamp > windowStart);

  if (requestsInWindow.length >= MAX_REQUESTS) {
    return false;
  }

  requestsInWindow.push(now);
  store.set(ip, requestsInWindow);

  return true;
}

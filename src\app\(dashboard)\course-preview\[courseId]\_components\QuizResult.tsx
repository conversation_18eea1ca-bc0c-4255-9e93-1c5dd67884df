import React from 'react';

type QuizResultProps = {
  totalQuestions: number;
  correctAnswers: number;
  onClose: () => void;
};

const QuizResult: React.FC<QuizResultProps> = ({ totalQuestions, correctAnswers, onClose }) => {
  const isSuccess = correctAnswers >= totalQuestions * 0.7;

  return (
    <div className={`p-6 rounded-lg shadow-lg max-w-md mx-auto ${isSuccess ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
      <h2 className="text-2xl font-bold mb-4 text-center">Quiz Completed!</h2>
      <p className="text-lg text-center">
        You got <span className="font-bold">{correctAnswers}</span> out of{' '}
        <span className="font-bold">{totalQuestions}</span> correct.
      </p>
      <div className="text-center">
        <button
          onClick={onClose}
          className={`mt-4 px-4 py-2 rounded ${isSuccess ? 'bg-green-500 hover:bg-green-600' : 'bg-red-500 hover:bg-red-600'} text-white`}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default QuizResult;

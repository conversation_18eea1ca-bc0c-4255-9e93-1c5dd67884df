
import { NextRequest, NextResponse } from 'next/server';
// @ts-ignore
import { GraphQLClient, gql } from 'graphql-request';
import { rateLimit } from '@/app/_utils/rateLimit';

const hygraphClient = new GraphQLClient(process.env.NEXT_PUBLIC_MASTER_URL, {
  headers: {
    authorization: `Bearer ${process.env.NEXT_PUBLIC_HYGRAPH_API_KEY}`,
  },
});

export async function POST(request: NextRequest) {
  const ip = request.headers.get('x-forwarded-for') || request.ip;
  
  if (!rateLimit(ip as string)) {
    return NextResponse.json({ success: false, message: "Too many requests. Please try again later." }, { status: 429 });
  }

  const { courseId, userEmail } = await request.json();

  if (!courseId || typeof courseId !== 'string' || !userEmail || typeof userEmail !== 'string') {
    return NextResponse.json({ success: false, message: "Invalid input" }, { status: 400 });
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(userEmail)) {
    return NextResponse.json({ success: false, message: "Invalid email format" }, { status: 400 });
  }

  try {
    const mutation = gql`
      mutation CreateAndPublishUserEnrollment($courseId: ID!, $userEmail: String!) {
        createUserEnrollment(
          data: {
            courseList: { connect: { id: $courseId } },
            userInfo: { connect: { email: $userEmail } },
            enrollmentDate: "${new Date().toISOString()}",
            enrolledStatus: Active
          }
        ) {
          id
        }
        publishUserEnrollment(where: { id: "" }) {
          id
        }
      }
    `;

    const result = await hygraphClient.request(mutation, { courseId, userEmail });

    if (result.createUserEnrollment) {
      const publishMutation = gql`
        mutation PublishUserEnrollment($id: ID!) {
          publishUserEnrollment(where: { id: $id }) {
            id
          }
        }
      `;
      await hygraphClient.request(publishMutation, { id: result.createUserEnrollment.id });
      return NextResponse.json({ success: true, enrollmentId: result.createUserEnrollment.id });
    } else {
      throw new Error("Failed to create user enrollment");
    }
  } catch (error) {
    console.error('Error enrolling user:', error.response?.errors || error);
    return NextResponse.json({ 
      success: false, 
      message: "An error occurred while enrolling in the course",
      error: error.response?.errors || error.message
    }, { status: 500 });
  }
}

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useUser } from "@clerk/nextjs";
import GlobalApi from "../_utils/GlobalApi";
import ExamList from "./_components/ExamList";
import NoAttemptsModal from "./_components/NoAttemptsModal";

function Exam() {
  const [exam, setExam] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasAttempts, setHasAttempts] = useState(true);
  const { user } = useUser();
  const userEmail = user?.emailAddresses?.[0]?.emailAddress;

  const getAllExams = useCallback(async () => {
    setIsLoading(true);
    try {
      const resp = await GlobalApi.getAllExamList();
      
      if (resp.exams && resp.exams.length > 0) {
        const currentExam = resp.exams[0];
        setExam(currentExam);
        
        // Check attempts
        if (userEmail) {
          const userInfo = await GlobalApi.getUserProfile(userEmail);
          if (userInfo) {
            const attempts = await GlobalApi.getExamAttempts(currentExam.id, userInfo.id);
            setHasAttempts(attempts ? attempts.attemptsLeft > 0 : true);
          }
        }
      } else {
        console.error("No exams found in response.");
      }
    } catch (error) {
      console.error("Failed to fetch exam:", error);
    } finally {
      setIsLoading(false);
    }
  }, [userEmail]);

  useEffect(() => {
    getAllExams();
  }, [getAllExams]);

  return (
    <div className="mt-4">
      {isLoading ? (
        <div className="flex justify-center items-center">
          <p>Loading...</p>
        </div>
      ) : (
        exam && (
          <>
            {!hasAttempts ? (
              <NoAttemptsModal 
                open={true} 
                onClose={() => {}} 
                examName={exam.examName.name} 
              />
            ) : (
              <div
                key={exam.id}
                className="group relative mb-4 w-full overflow-hidden rounded-xl bg-white text-gray-800 shadow-one duration-300 hover:shadow-two"
              >
                <ExamList exam={exam} onAttemptsExhausted={() => setHasAttempts(false)} />
              </div>
            )}
          </>
        )
      )}
    </div>
  );
}

export default Exam;

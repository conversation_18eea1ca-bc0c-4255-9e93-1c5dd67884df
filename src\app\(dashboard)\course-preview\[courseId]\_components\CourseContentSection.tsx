"use client";
import React, { useState, useEffect } from "react";
import { Lock, Play, NotebookPen } from "lucide-react";
import <PERSON> from "next/link";
import { Button } from "@/components/ui/button";
import QuizModal from "../_components/QuizModal";

function CourseContentSection({ courseInfo, setActiveChapterIndex }) {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedQuiz, setSelectedQuiz] = useState(null);
  const [completedItems, setCompletedItems] = useState({});
  const [allQuizzesCompleted, setAllQuizzesCompleted] = useState(false);

  // Ensure courseInfo is available
  useEffect(() => {
  }, [courseInfo]);

  if (!courseInfo || !courseInfo.chapter) {
    return <div>Loading...</div>;
  }

  const handleChapterClick = (index) => {
    setActiveIndex(index);
    setActiveChapterIndex(index);
    setIsModalOpen(false);
  };

  const handleQuizClick = (quizData) => {
    setSelectedQuiz(quizData);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedQuiz(null);
  };

  const handleCheckboxChange = (index, type) => {
    setCompletedItems((prev) => {
      const newState = { ...prev, [`${type}-${index}`]: !prev[`${type}-${index}`] };

      // Automatically unlock the next item in sequence when the current one is completed
      if (newState[`${type}-${index}`]) {
        const nextIndex = index + 1;
        if (nextIndex < courseInfo.chapter.length) {
          const nextType = type === "chapter" ? "quiz" : "chapter";
          newState[`${nextType}-${nextIndex}`] = false; // Ensure the next item is unlocked
        }
      }

      // Check if all quizzes are completed
      const allQuizzesCompleted = contentItems
        .filter(item => item.type === 'quiz')
        .every(item => newState[`quiz-${item.index}`]);

      setAllQuizzesCompleted(allQuizzesCompleted);

      return newState;
    });
  };

  const getContentItems = () => {
    const contentItems = [];

    courseInfo.chapter.forEach((chapter, index) => {
      contentItems.push({
        type: "chapter",
        index: index,
        name: chapter.name,
        moduleQuizAndChoices: chapter.moduleQuizAndChoices,
      });

      if (chapter.moduleQuizAndChoices && chapter.moduleQuizAndChoices.length > 0) {
        contentItems.push({
          type: "quiz",
          index: index,
          name: chapter.moduleQuizAndChoices[0].module,
          quizData: chapter.moduleQuizAndChoices,
        });
      }
    });

    return contentItems;
  };

  const contentItems = getContentItems();

  return (
    <div className="rounded-sm bg-white p-3 text-dark">
      <h2>Contents</h2>

      {contentItems.map((item, idx) => {
        const isCompleted = completedItems[`${item.type}-${item.index}`];
        const isPrevCompleted =
          idx === 0 || completedItems[`${contentItems[idx - 1].type}-${contentItems[idx - 1].index}`];
        const isDisabled = !isPrevCompleted && !isCompleted;

        return (
          <div key={idx} className="flex items-center">
            {item.type === "chapter" ? (
              <>
                <h2
                  className={`flex-grow m-2 flex cursor-pointer items-center justify-between rounded-sm border p-2 px-4 text-[14px] 
                    ${
                      isDisabled
                        ? "disabled-item"
                        : isCompleted
                        ? "bg-green-500 text-white completed-item"
                        : activeIndex === item.index
                        ? "bg-primary text-white active-item"
                        : ""
                    }`}
                  onClick={() => isPrevCompleted && handleChapterClick(item.index)}
                >
                  {item.index + 1}. {item.name}
                  {isCompleted || isPrevCompleted ? (
                    <Play className="h-4 w-4" />
                  ) : (
                    <Lock className="h-4 w-4" />
                  )}
                </h2>
                <input
                  type="checkbox"
                  checked={isCompleted || false}
                  onChange={() => handleCheckboxChange(item.index, "chapter")}
                  className="mr-2"
                  disabled={isDisabled}
                />
              </>
            ) : (
              <>
                <h2
                  className={`flex-grow m-2 flex cursor-pointer items-center justify-between rounded-sm border p-2 px-4 text-[14px] 
                    ${
                      isDisabled
                        ? "disabled-item"
                        : isCompleted
                        ? "bg-green-500 text-white completed-item"
                        : ""
                    }`}
                  onClick={() => isPrevCompleted && handleQuizClick(item.quizData)}
                >
                  {item.name}
                  {isCompleted || isPrevCompleted ? (
                    <Play className="h-4 w-4" />
                  ) : (
                    <Lock className="h-4 w-4" />
                  )}
                </h2>
                <input
                  type="checkbox"
                  checked={isCompleted || false}
                  onChange={() => handleCheckboxChange(item.index, "quiz")}
                  className="mr-2"
                  disabled={isDisabled}
                />
              </>
            )}
          </div>
        );
      })}

      <div className="items-center justify-center">
        <Button 
          className={`m-2 mr-2 flex w-full cursor-pointer rounded-sm border p-2 text-[14px] text-white ${
            allQuizzesCompleted ? 'hover:bg-gray-200 hover:text-gray-500' : 'opacity-50 cursor-not-allowed'
          }`}
          disabled={!allQuizzesCompleted}
          onClick={() => {
            if (allQuizzesCompleted) {
              window.location.href = '/exam';
            } else {
              alert('Please complete all quizzes before taking the exam.');
            }
          }}
        >
          <NotebookPen className="m-1 h-5 w-5 text-white hover:text-gray-500" />
          Take Exam
        </Button>
      </div>

      {/* Modal for displaying quizzes */}
      {isModalOpen && selectedQuiz && (
        <QuizModal selectedQuiz={selectedQuiz} closeModal={closeModal} />
      )}
    </div>
  );
}

export default CourseContentSection;

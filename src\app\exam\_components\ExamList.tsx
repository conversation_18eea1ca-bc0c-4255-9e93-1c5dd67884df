import React, { useState, useEffect } from 'react';
import ResultModal from './ResultModal';
import Timer from './Timer';
import { useUser } from "@clerk/nextjs";
import GlobalApi from '@/app/_utils/GlobalApi';
import { useRouter } from 'next/navigation';

interface ExamListProps {
  exam: {
    id: string;
    examDate: string;
    examDescription: { markdown: string };
    examDuration: number;
    examName: { name: string; isOnLine: boolean };
    examScore: number;
    questionsAndChoices: {
      questionsAndChoices: Array<{
        question: string;
        options: string[];
      }>;
    }[];
  };
  onAttemptsExhausted?: () => void;
}

const ExamList: React.FC<ExamListProps> = ({ exam, onAttemptsExhausted }) => {
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: string]: string | string[] }>({});
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [resultPercentage, setResultPercentage] = useState<number | null>(null);
  const [passed, setPassed] = useState(false);
  const [attemptsLeft, setAttemptsLeft] = useState<number | null>(null);
  const [isLoadingAttempts, setIsLoadingAttempts] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  const { user } = useUser();
  const userEmail = user?.emailAddresses?.[0]?.emailAddress;
  const userName = user ? `${user.firstName} ${user.lastName}` : '';
  const router = useRouter();

  const [shuffledQuestions, setShuffledQuestions] = useState([]);

  useEffect(() => {
    const shuffled = [...(exam?.questionsAndChoices?.[0]?.questionsAndChoices || [])].sort(() => Math.random() - 0.5);
    setShuffledQuestions(shuffled);
  }, [exam]);

  useEffect(() => {
    const fetchAttempts = async () => {
      if (!user?.emailAddresses?.[0]?.emailAddress) return;
      try {
        const userInfo = await GlobalApi.getUserProfile(user.emailAddresses[0].emailAddress);
        if (!userInfo) return;

        const attempts = await GlobalApi.getExamAttempts(exam.id, userInfo.id);
        const remainingAttempts = attempts ? attempts.attemptsLeft : 2;
        setAttemptsLeft(remainingAttempts);
        
        // Notify parent if no attempts left
        if (remainingAttempts <= 0 && onAttemptsExhausted) {
          onAttemptsExhausted();
        }
      } catch (error) {
        console.error('Error fetching exam attempts:', {
          examId: exam.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        setAttemptsLeft(2); // Default to 2 if there's an error
      } finally {
        setIsLoadingAttempts(false);
      }
    };

    fetchAttempts();
  }, [exam.id, user, onAttemptsExhausted]);

  const handleAnswerChange = (question: string, choice: string) => {
    if (!hasStarted) {
      setHasStarted(true);
    }

    setSelectedAnswers({
      ...selectedAnswers,
      [question]: choice,
    });
    setErrors({
      ...errors,
      [question]: '',
    });
  };

  const handleCheckboxChange = (question: string, choice: string) => {
    if (!hasStarted) {
      setHasStarted(true);
    }

    const currentAnswers = selectedAnswers[question] as string[] || [];
    const updatedAnswers = currentAnswers.includes(choice)
      ? currentAnswers.filter((answer) => answer !== choice)
      : [...currentAnswers, choice];

    setSelectedAnswers({
      ...selectedAnswers,
      [question]: updatedAnswers,
    });
    setErrors({
      ...errors,
      [question]: '',
    });
  };

  const handleTimeUp = () => {
    if (!isSubmitting) {
      handleSubmit(null);
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement> | null) => {
    if (event) {
      event.preventDefault();
    }

    if (isSubmitting) return;
    setIsSubmitting(true);

    const formattedAnswers = {};
    shuffledQuestions.forEach((question) => {
      const questionId = question.question;
      const answer = selectedAnswers[questionId];
      formattedAnswers[questionId] = answer;
    });

    try {
      const response = await fetch('/api/submit-exam', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          examId: exam.examName.name,
          answers: formattedAnswers,
          email: userEmail,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        if (response.status === 403) {
          // No attempts left
          setAttemptsLeft(0);
          throw new Error(result.error || 'No more attempts left for this exam');
        }
        throw new Error(result.error || 'Failed to submit exam');
      }

      setResultPercentage(result.percentage);
      setPassed(result.passed);
      setIsModalOpen(true);

      if (!result.passed) {
        const newAttempts = attemptsLeft !== null ? Math.max(0, attemptsLeft - 1) : 0;
        setAttemptsLeft(newAttempts);
        
        if (newAttempts <= 0 && onAttemptsExhausted) {
          onAttemptsExhausted();
        }
      }
    } catch (error) {
      console.error('Error submitting exam:', {
        examId: exam.examName.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // Show error in UI
      alert(error instanceof Error ? error.message : 'Failed to submit exam');
    } finally {
      setIsSubmitting(false);
    }
  };

  const progress = Math.round((Object.keys(selectedAnswers).length / shuffledQuestions.length) * 100);

  const resetExam = () => {
    setSelectedAnswers({});
    setErrors({});
    setResultPercentage(null);
    setPassed(false);
    setHasStarted(false);
  };

  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div className="p-8 relative">
      {hasStarted && (
        <Timer
          duration={exam.examDuration}
          onTimeUp={handleTimeUp}
          isActive={hasStarted && !isModalOpen}
        />
      )}

      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <h1 className="text-3xl font-bold">{exam.examName.name}</h1>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors flex items-center"
          >
            <span>← Back to Dashboard</span>
          </button>
        </div>
        <p className="text-gray-600 mb-6">{exam.examDescription.markdown}</p>
        <div className="bg-blue-50 p-6 rounded-lg shadow-sm">
          <p className="font-semibold text-lg mb-3">Exam Details:</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-700"><span className="font-medium">Duration:</span> {exam.examDuration} minutes</p>
              <p className="text-gray-700"><span className="font-medium">Pass Score:</span> {exam.examScore}%</p>
            </div>
            <div>
              <p className="text-gray-700"><span className="font-medium">Attempts Left:</span> {isLoadingAttempts ? '...' : attemptsLeft}</p>
              <p className="text-gray-700"><span className="font-medium">Progress:</span> {progress}%</p>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {shuffledQuestions.map((question, index) => (
          <div key={index} className="mb-6 p-6 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <p className="font-semibold text-lg mb-4">
              {index + 1}. {question.question}
            </p>
            <div className="space-y-3 pl-4">
            {question.options.map((option, optionIndex) => (
  <div
    key={optionIndex}//index+optionIndex
    className="flex items-center p-2 hover:bg-gray-50 rounded-md transition-colors"
  >
    <input
      type="radio" // Changed from "checkbox" to "radio"
      id={`${index}-${optionIndex}`}
      name={`question-${index}`} // Ensure all options for the question share the same "name"
      value={option}
      checked={selectedAnswers[question.question] === option}
      onChange={() => handleAnswerChange(question.question, option)}
      className="w-4 h-4 mr-3 text-blue-600 focus:ring-blue-500"
    />
    <label
      htmlFor={`${index}-${optionIndex}`}
      className="flex-grow cursor-pointer py-1"
    >
      {option}
    </label>
  </div>
))}
            </div>
            {errors[question.question] && (
              <p className="text-red-500 mt-2 text-sm">{errors[question.question]}</p>
            )}
          </div>
        ))}

        <div className="sticky bottom-0 left-0 right-0 bg-white bg-opacity-90 backdrop-blur-sm p-4 border-t shadow-lg">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {progress}% Complete ({Object.keys(selectedAnswers).length}/{shuffledQuestions.length} questions answered)
            </div>
            <button
              type="submit"
              className={`px-8 py-3 rounded-lg font-semibold transition-colors ${
                isSubmitting || attemptsLeft === 0
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
              disabled={isSubmitting || attemptsLeft === 0}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Exam'}
            </button>
          </div>
        </div>
      </form>

      {isModalOpen && resultPercentage !== null && (
        <ResultModal
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          passed={passed}
          resultPercentage={resultPercentage}
          userName={userName}
          examName={exam.examName.name}
          examScore={exam.examScore}
          attemptsLeft={attemptsLeft}
          onTryAgain={() => {
            setIsModalOpen(false);
            resetExam();
          }}
        />
      )}
    </div>
  );
};

export default ExamList;

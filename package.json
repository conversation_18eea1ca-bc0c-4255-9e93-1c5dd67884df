{"name": "global-safe-skyz", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.2.0", "@clerk/backend": "^1.17.2", "@clerk/clerk-react": "^5.15.5", "@clerk/clerk-sdk-node": "^5.0.68", "@clerk/nextjs": "^6.3.4", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.1.3", "@nextui-org/react": "^2.4.6", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.1.0", "@react-pdf/renderer": "^3.4.4", "@react-three/drei": "^9.112.0", "@react-three/fiber": "^8.17.7", "@types/next-auth": "^3.15.0", "auth": "^1.0.2", "axios": "^1.7.7", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "framer-motion": "^11.5.4", "graphql": "^16.9.0", "graphql-request": "^3.7.0", "init": "^0.1.2", "isomorphic-fetch": "^3.0.0", "lucide-react": "^0.394.0", "markdown-library": "^0.1.0", "next": "^14.1.0", "next-themes": "^0.2.1", "next-video": "^1.1.0", "nodemailer": "^6.9.16", "puppeteer": "^24.14.0", "react": "^18.2.0", "react-confetti": "^6.1.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-modal": "^3.16.1", "react-modal-video": "^2.0.1", "react-select": "^5.9.0", "react-slick": "^0.30.2", "react-toastify": "^10.0.6", "resend": "^4.0.1-alpha.0", "slick-carousel": "^1.8.1", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "three": "^0.168.0", "three-globe": "^2.31.1"}, "devDependencies": {"@types/node": "^20.8.9", "@types/react": "^18.2.33", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.4", "typescript": "^5.3.3"}}
import Image from "next/image";
import Link from "next/link";

const RelatedPost = ({
  image,
  slug,
  title,

}: {
  image: string;
  slug: string;
  title: string;
 
}) => {
  return (
    <div className="flex items-center lg:block xl:flex">
      <div className="w-full">
        <h5 className=" block text-base font-medium leading-snug text-white hover:text-white dark:text-white dark:hover:text-">
            {title}
        </h5>
      </div>
    </div>
  );
};

export default RelatedPost;
